import { AsyncLocalStorage } from 'node:async_hooks';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import type {
  LangfuseEventClient,
  LangfuseGenerationClient,
  LangfuseSpanClient,
  LangfuseTraceClient,
} from 'langfuse';
import Langfuse from 'langfuse';
import {
  CreateLangfuseEventBody,
  CreateLangfuseGenerationBody,
  CreateLangfuseSpanBody,
  CreateLangfuseTraceBody,
  UpdateLangfuseGenerationBody,
} from 'langfuse-core';
import { SystemConfig } from '../types';
import { getEnv } from '../utils';
import { YouapiClsService } from './cls.service';

/**
 * 跟踪上下文堆栈，用于处理每个请求的多个跟踪
 * 每个跟踪都有自己的上下文并且可以嵌套
 */
interface LangfuseTraceContext {
  trace: LangfuseTraceClient;
  traceId: string;
  parentTrace?: LangfuseTraceClient;
  spans: Map<string, LangfuseSpanClient>;
  generations: Map<string, LangfuseGenerationClient>;
  metadata: Record<string, any>;
}

/**
 * LangfuseTraceService - 在 NestJS 中管理 Langfuse 跟踪上下文
 *
 * 此服务提供：
 * - 使用 AsyncLocalStorage 的请求范围的跟踪上下文
 * - 支持每个请求的多个跟踪（跟踪堆栈）
 * - 与现有 Langfuse 基础设施的集成
 * - 优雅处理缺少的跟踪上下文
 *
 * 用法：
 * ```typescript
 * // 在服务或控制器中
 * constructor(private readonly traceService: LangfuseTraceService) {}
 *
 * async myMethod() {
 *   // 创建新跟踪
 *   await this.traceService.withTrace({ name: 'my-operation' }, async () => {
 *     // 使用当前跟踪
 *     this.traceService.addSpan({ name: 'step-1' });
 *   });
 * }
 * ```
 */
@Injectable()
export class LangfuseTraceService {
  // 用于跟踪上下文的 AsyncLocalStorage - 支持每个请求的多个跟踪
  private readonly contextStorage = new AsyncLocalStorage<LangfuseTraceContext[]>();
  private readonly langfuse: Langfuse;
  private readonly logger = new Logger(LangfuseTraceService.name);

  constructor(
    private readonly configService: ConfigService<SystemConfig>,
    private readonly youapiClsService: YouapiClsService,
  ) {
    // 使用环境变量初始化 Langfuse
    // 这些应该在您的环境或 .env 文件中设置
    const baseUrl = this.configService.getOrThrow<string>('LANGFUSE_BASE_URL');
    const publicKey = this.configService.getOrThrow<string>('LANGFUSE_PUBLIC_KEY');
    const secretKey = this.configService.getOrThrow<string>('LANGFUSE_SECRET_KEY');

    if (!baseUrl || !publicKey || !secretKey) {
      this.logger.error('无效配置，Langfuse 跟踪将被禁用');
      // 创建一个什么都不做的模拟 Langfuse 实例
      this.langfuse = {
        trace: () => ({
          span: () => ({ id: '', end: () => {} }),
          generation: () => ({ id: '', end: () => {} }),
          event: () => {},
          update: () => {},
        }),
        flushAsync: () => Promise.resolve(),
        shutdownAsync: () => Promise.resolve(),
      } as any;
      return;
    }

    this.langfuse = new Langfuse({
      baseUrl,
      publicKey,
      secretKey,
      enabled: process.env.NODE_ENV !== 'test',
    });
  }

  /**
   * 获取当前跟踪上下文堆栈
   */
  private getContextStack(): LangfuseTraceContext[] {
    return this.contextStorage.getStore() || [];
  }

  /**
   * 获取当前（顶级）跟踪上下文
   */
  getCurrentTraceContext(): LangfuseTraceContext | undefined {
    const stack = this.getContextStack();
    return stack[stack.length - 1];
  }

  /**
   * 获取当前跟踪客户端
   */
  getCurrentTrace(): LangfuseTraceClient | undefined {
    return this.getCurrentTraceContext()?.trace;
  }

  /**
   * 获取当前跟踪客户端
   */
  getCurrentGeneration(): LangfuseGenerationClient | undefined {
    const context = this.getCurrentTraceContext();
    if (!context?.generations) {
      return undefined;
    }
    return Array.from(context.generations.values()).pop();
  }

  /**
   * 获取当前跟踪 ID
   */
  getCurrentTraceId(): string | undefined {
    return this.getCurrentTraceContext()?.traceId;
  }

  /**
   * 检查我们当前是否在跟踪上下文中
   */
  hasActiveTrace(): boolean {
    return this.getCurrentTrace() !== undefined;
  }

  /**
   * 创建新跟踪并在其上下文中执行函数
   * @param options 跟踪创建选项
   * @param fn 在跟踪上下文中执行的函数
   * @returns 包含函数结果的 Promise
   */
  async withTrace<T>(
    options: CreateLangfuseTraceBody & { inheritTrace?: boolean },
    fn: () => Promise<T>,
  ): Promise<T> {
    const currentStack = this.getContextStack();
    const parentContext = currentStack[currentStack.length - 1];

    this.logger.log(
      `withTrace called: name=${options.name}, currentStackLength=${currentStack.length}, hasParent=${!!parentContext}`,
    );

    // 如果要创建新的独立 trace（不是嵌套 trace），先 flush 之前的 trace
    const isNestedTrace = options.inheritTrace && parentContext;
    if (!isNestedTrace && currentStack.length > 0) {
      try {
        await this.flush();
        this.logger.debug('Flushed previous traces before creating new independent trace');
      } catch (flushError) {
        this.logger.warn('Failed to flush previous traces:', flushError);
      }
    }

    // 获取当前请求的跟踪元数据
    const currentUserId = this.youapiClsService.getUserId();

    // 合并元数据，优先使用传入的选项，然后使用当前请求的元数据
    const mergedMetadata = {
      // 当前请求的元数据
      ...this.getCurrentRequestMetadata(),
      // 传入的元数据（会覆盖上面的值）
      ...options.metadata,
    };

    // 创建新跟踪
    const traceId = options.id || crypto.randomUUID();
    let trace: LangfuseTraceClient;

    if (isNestedTrace) {
      // 当需要继承时，直接使用父跟踪而不是创建新的
      trace = parentContext.trace;
    } else {
      // 创建新的独立跟踪
      const traceOptions = {
        id: traceId,
        name: options.name || 'unknown-trace',
        userId: options.userId || currentUserId, // 使用当前请求的 userId 作为默认值
        sessionId: options.sessionId,
        metadata: mergedMetadata,
        tags: [getEnv()],
        input: options.input, // 传递 input 到 trace 级别
        output: options.output, // 传递 output 到 trace 级别
      };

      this.logger.debug(`Creating new trace: ${traceId} with name: ${traceOptions.name}`);
      trace = this.langfuse.trace(traceOptions);
    }

    const newContext: LangfuseTraceContext = {
      trace,
      traceId,
      parentTrace: parentContext?.trace,
      spans: new Map(),
      generations: new Map(),
      metadata: mergedMetadata,
    };

    // 添加到堆栈
    const newStack = [...currentStack, newContext];

    // 在新上下文中执行函数
    this.logger.log(
      `Running function in trace context: ${traceId}, stack depth: ${newStack.length}`,
    );

    const result = await this.contextStorage.run(newStack, fn);

    // 验证上下文是否仍然存在
    const finalContext = this.getCurrentTraceContext();
    this.logger.log(
      `After execution - final context: ${finalContext?.traceId}, active traces: ${this.getContextStack().length}`,
    );

    return result;
  }

  /**
   * Create a new trace if none exists, otherwise use existing
   * @param options Trace creation options
   * @param fn Function to execute
   */
  async withTraceOrCurrent<T>(options: CreateLangfuseTraceBody, fn: () => Promise<T>): Promise<T> {
    if (this.hasActiveTrace()) {
      const additionalMetadata = {
        ...this.getCurrentRequestMetadata(),
        ...options.metadata,
      };

      if (Object.keys(additionalMetadata).length > 0) {
        this.updateTraceMetadata(additionalMetadata);
      }

      return fn();
    }
    return this.withTrace(options, fn);
  }

  /**
   * Add a span to the current trace. If no trace exists, creates a new trace automatically.
   * @param options Span options
   * @param inheritTrace Whether to inherit existing trace context (default: true)
   * @returns Span client
   */
  async addSpan(
    options: CreateLangfuseSpanBody,
    inheritTrace: boolean = true,
  ): Promise<LangfuseSpanClient> {
    const context = this.getCurrentTraceContext();

    // If no active trace or inheritTrace is false, create a new trace
    if (!context || !inheritTrace) {
      const traceName = options.name || 'auto-span';

      // Create a new trace context
      return await this.withTrace(
        {
          name: traceName,
          metadata: {
            ...this.getCurrentRequestMetadata(),
            ...options.metadata,
            inheritTrace,
            input: options.input,
          },
          tags: [getEnv()],
        },
        async () => {
          const newContext = this.getCurrentTraceContext();
          if (!newContext) {
            throw new Error('Failed to create trace context for span');
          }

          const span = newContext.trace.span(options);
          newContext.spans.set(span.id, span);
          return span;
        },
      );
    }

    let span;
    const generation = this.getCurrentGeneration();
    if (generation) {
      span = generation.span(options);
    } else {
      span = context.trace.span(options);
    }
    context.spans.set(span.id, span);
    return span;
  }

  /**
   * Add an event to the current trace. If no trace exists, creates a new trace automatically.
   * @param options Event options
   * @param inheritTrace Whether to inherit existing trace context (default: true)
   */
  async addEvent(
    options: CreateLangfuseEventBody,
    inheritTrace: boolean = true,
  ): Promise<LangfuseEventClient> {
    const context = this.getCurrentTraceContext();

    // If no active trace or inheritTrace is false, create a new trace
    if (!context || !inheritTrace) {
      const traceName = options.name || 'auto-event';

      // Create a new trace context
      return await this.withTrace(
        {
          name: traceName,
          metadata: {
            ...this.getCurrentRequestMetadata(),
            ...options.metadata,
            inheritTrace,
            input: options.input,
          },
          tags: [getEnv()],
        },
        async () => {
          const newContext = this.getCurrentTraceContext();
          if (!newContext) {
            throw new Error('Failed to create trace context for event');
          }

          return newContext.trace.event(options);
        },
      );
    }

    let event;
    const generation = this.getCurrentGeneration();
    if (generation) {
      event = generation.event(options);
    } else {
      event = context.trace.event(options);
    }
    return event;
  }

  /**
   * Add a generation to the current trace. If no trace exists, creates a new trace automatically.
   * @param options Generation options
   * @param inheritTrace Whether to inherit existing trace context (default: true)
   * @returns Generation client
   */
  async addGeneration(
    options: CreateLangfuseGenerationBody,
    inheritTrace: boolean = true,
  ): Promise<LangfuseGenerationClient> {
    const context = this.getCurrentTraceContext();

    // If no active trace or inheritTrace is false, create a new trace
    if (!context || !inheritTrace) {
      const traceName = options.name || 'auto-generation';

      // Create a new trace context
      return await this.withTrace(
        {
          name: traceName,
          metadata: {
            ...this.getCurrentRequestMetadata(),
            ...options.metadata,
            inheritTrace,
          },
          tags: [getEnv()],
          input: options.input,
        },
        async () => {
          const newContext = this.getCurrentTraceContext();
          if (!newContext) {
            throw new Error('Failed to create trace context for generation');
          }

          this.logger.log(`Creating generation in new trace: ${options.name}`);
          this.logger.log(
            `Generation options:`,
            JSON.stringify({
              name: options.name,
              model: options.model,
              hasInput: !!options.input,
              inputType: typeof options.input,
              inputLength: Array.isArray(options.input) ? options.input.length : 'not-array',
            }),
          );
          const generation = newContext.trace.generation(options);
          newContext.generations.set(generation.id, generation);
          this.logger.log(`Generation created with ID: ${generation.id}`);
          return generation;
        },
      );
    }

    // Use existing trace context
    if (!options.startTime) {
      options.startTime = new Date();
    }
    const generation = context.trace.generation(options);
    context.generations.set(generation.id, generation);
    this.logger.log(`Generation created with ID: ${generation.id}`);
    return generation;
  }

  addTrace(options: CreateLangfuseTraceBody): LangfuseTraceClient {
    const trace = this.langfuse.trace(options);
    return trace;
  }

  /**
   * Update the current trace with additional metadata
   * @param metadata Metadata to add
   */
  updateTraceMetadata(metadata: Record<string, any>): void {
    const context = this.getCurrentTraceContext();
    if (!context) {
      return;
    }

    context.metadata = { ...context.metadata, ...metadata };
    context.trace.update({
      metadata: context.metadata,
    });
  }

  /**
   * Update the current trace with input/output data
   * @param data Data to update (input, output, metadata)
   */
  updateTrace(data: { input?: any; output?: any; metadata?: Record<string, any> }): void {
    const context = this.getCurrentTraceContext();
    if (!context) {
      this.logger.warn('updateTrace called but no active trace context');
      return;
    }

    this.logger.log(
      `updateTrace called:`,
      JSON.stringify({
        traceId: context.traceId,
        hasInput: !!data.input,
        hasOutput: !!data.output,
        hasMetadata: !!data.metadata,
        inputType: typeof data.input,
        outputType: typeof data.output,
      }),
    );

    if (data.metadata) {
      context.metadata = { ...context.metadata, ...data.metadata };
    }

    const updateData = {
      input: data.input,
      output: data.output,
      metadata: context.metadata,
    };

    this.logger.log(
      `Calling trace.update with:`,
      JSON.stringify({
        hasInput: !!updateData.input,
        hasOutput: !!updateData.output,
        hasMetadata: !!updateData.metadata,
      }),
    );

    context.trace.update(updateData);
  }

  updateGeneration(data: Partial<UpdateLangfuseGenerationBody>): void {
    const context = this.getCurrentGeneration();
    if (!context) {
      return;
    }

    context.update(data);
  }

  /**
   * Flush all pending traces
   */
  async flush(): Promise<void> {
    const stats = this.getTraceStats();
    this.logger.log(
      `Flushing traces - Active: ${stats.activeTraces}, Current: ${stats.currentTraceId}, Spans: ${stats.totalSpans}`,
    );

    try {
      await this.langfuse.flushAsync();
      this.logger.log('Langfuse flush completed successfully');
    } catch (error) {
      this.logger.error('Langfuse flush failed:', error);
      throw error;
    }
  }

  /**
   * Shutdown the Langfuse client
   */
  async shutdown(): Promise<void> {
    await this.langfuse.shutdownAsync();
  }

  /**
   * Get trace statistics for debugging
   */
  getTraceStats(): {
    activeTraces: number;
    currentTraceId?: string;
    totalSpans: number;
  } {
    const context = this.getCurrentTraceContext();
    const stack = this.getContextStack();

    return {
      activeTraces: stack.length,
      currentTraceId: context?.traceId,
      totalSpans: context?.spans.size || 0,
    };
  }

  /**
   * Get current request trace metadata
   * @returns Object containing current traceId, userId, and spaceId
   */
  getCurrentRequestMetadata(): {
    traceId?: string;
    userId?: string;
    spaceId?: string;
  } {
    return {
      userId: this.youapiClsService.getUserId(),
      spaceId: this.youapiClsService.getSpaceId(),
      traceId: this.youapiClsService.getTraceId(),
    };
  }

  /**
   * Run a function in the current async context
   * This ensures that the async storage context is preserved for streaming operations
   * @param fn Function to execute in the current context
   * @returns Promise with the function result
   */
  async runInCurrentContext<T>(fn: () => Promise<T>): Promise<T> {
    const currentStack = this.getContextStack();
    if (currentStack.length === 0) {
      // No active context, just run the function
      return fn();
    }

    // Run the function in the current async storage context
    return this.contextStorage.run(currentStack, fn);
  }
}
