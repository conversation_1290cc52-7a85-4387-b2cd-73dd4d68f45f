/**
 * 主应用模块
 * 集成了从 youapp 迁移过来的中间件、错误处理、认证等功能
 */

import {
  type MiddlewareConsumer,
  Module,
  type NestModule,
  type OnModuleInit,
} from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ModuleRef } from '@nestjs/core';

import { CqrsModule } from '@nestjs/cqrs';
import { ClsModule } from 'nestjs-cls';
import { OpenTelemetryModule } from 'nestjs-otel';
import { AppController } from './app.controller';
import { CommonModule } from './common/common.module';
import { TracingMiddleware } from './common/middleware/tracing.middleware';
import { ApplicationContext } from './common/utils/application-context';
import { DaoModule } from './dao/dao.module';
import { DomainModule } from './domain/domain.module';
import { HealthController } from './health/health.controller';
import { InfraModule } from './infra/infra.module';
import { AiModule } from './modules/ai/ai.module';
import { ChatModule } from './modules/chat/chat.module';
import { IamModule } from './modules/iam/iam.module';
import { MaterialMngModule } from './modules/material-mng/material-mng.module';
import { SearchModule } from './modules/search/search.module';
import { TestModule } from './modules/test/test.module';
import { FixJsonInFormUrlencodedMiddleware } from './shared/fix-json-in-form-urlencoded.middleware';

@Module({
  imports: [
    // 配置模块必须放在最前面
    ConfigModule.forRoot({
      isGlobal: true,
      cache: true,
      envFilePath: [`.env.${process.env.YOUMIND_ENV}.local`, `.env.${process.env.YOUMIND_ENV}`],
    }),

    // CLS 模块用于管理请求级别的数据
    ClsModule.forRoot({
      global: true,
      middleware: { mount: true },
    }),

    // OpenTelemetry 模块用于追踪请求
    OpenTelemetryModule.forRoot(),
    // LoggerModule.forRootAsync({
    //   useFactory: (clsService: YouapiClsService) => ({
    //     pinoHttp: {
    //       genReqId: () => clsService.getTraceId() || '',
    //       customProps: () => {
    //         return {
    //           time: Date.now(),
    //           userId: clsService.getUserId(),
    //           spaceId: clsService.getSpaceId(),
    //         };
    //       },
    //     },
    //   }),
    //   inject: [YouapiClsService],
    // }),

    // DevtoolsModule.register({
    //   http: process.env.NODE_ENV === 'development',
    // }),

    // CQRS 模块用于处理命令和事件
    CqrsModule.forRoot(),

    // 通用模块提供中间件、日志等服务
    CommonModule,

    // 数据访问模块
    DaoModule,

    // 基础设施模块提供三方服务
    InfraModule,

    // 领域模块提供业务服务
    DomainModule,

    MaterialMngModule,
    IamModule,
    ChatModule,
    SearchModule,
    AiModule,
    TestModule,
  ],
  controllers: [AppController, HealthController],
})
export class AppModule implements NestModule, OnModuleInit {
  constructor(private moduleRef: ModuleRef) {}

  onModuleInit() {
    ApplicationContext.register(this.moduleRef);
  }

  configure(consumer: MiddlewareConsumer) {
    // 配置全局中间件
    consumer.apply(TracingMiddleware, FixJsonInFormUrlencodedMiddleware).forRoutes('*'); // 应用到所有路由
  }
}
