import { Logger } from '@nestjs/common';
import { AggregateRoot } from '@nestjs/cqrs';
import { uuidv7 } from 'uuidv7';
import { SafeParse } from '@/common/utils';
import { BoardPositionDomainService, BoardPositionInfo } from '../../shared/board-position.service';
import { SnipCreatedEvent } from '../events/snip-created.event';
import { SnipDeletedEvent } from '../events/snip-deleted.event';
import { SnipUpdatedEvent } from '../events/snip-updated.event';

// Snip 类型常量
export const SnipType = {
  ARTICLE: 'article',
  SNIPPET: 'snippet',
  IMAGE: 'image',
  VOICE: 'voice',
  VIDEO: 'video',
  PDF: 'pdf',
  OFFICE: 'office',
  TEXT_FILE: 'text-file',
  OTHER_WEBPAGE: 'other-webpage',
  UNKNOWN_WEBPAGE: 'unknown-webpage',
  THOUGHT: 'thought', // deprecated
} as const;

export type SnipType = (typeof SnipType)[keyof typeof SnipType];

// Snip 来源常量
export const SnipFrom = {
  WEBPAGE: 'webpage',
  FILE: 'file',
  SNIPPET: 'snippet',
  THOUGHT: 'thought', // deprecated
} as const;

export type SnipFrom = (typeof SnipFrom)[keyof typeof SnipFrom];

// Snip 状态常量
export const SnipStatus = {
  FETCHING: 'fetching',
  FETCH_FAILED: 'fetch-failed',
  IMAGE_TRANSFERING: 'image-transfering',
  IMAGE_TRANSFER_FAILED: 'image-transfer-failed',
  PDF_PARSING: 'pdf-parsing',
  PDF_PARSE_FAILED: 'pdf-parse-failed',
} as const;

export type SnipStatus = (typeof SnipStatus)[keyof typeof SnipStatus];

// TODO 看是否可以抽出去，和 thought 的 VisibilityType 合并
export type VisibilityType = 'private' | 'public';

// 内部数据结构，仅用于 API 兼容性
export interface BoardItemInfo {
  id: string;
  boardId: string;
  parentBoardGroupId?: string;
  boardGroupId?: string;
  rank: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateSnipParam {
  id?: string;
  spaceId: string;
  creatorId: string;
  parentId?: string;
  title: string;
  extra?: string;
  type?: SnipType;
  from?: SnipFrom;
  status?: SnipStatus;
  visibility?: VisibilityType;
  boardId?: string;
  parentBoardGroupId?: string; // 新增：支持在分组内创建
}

export interface UpdateSnipParam {
  title?: string;
  status?: SnipStatus;
  visibility?: VisibilityType;
  extra?: string;
}

export class Snip extends AggregateRoot {
  private static readonly logger = new Logger(Snip.name);
  public isNew: boolean = false;

  constructor(
    public readonly id: string,
    public readonly createdAt: Date,
    public updatedAt: Date,
    public readonly creatorId: string,
    public readonly spaceId: string,
    public readonly parentId: string | undefined,

    public title: string,
    public extra: string | undefined,

    public readonly type: SnipType,
    public readonly from: SnipFrom,
    public status: SnipStatus | undefined,
    public visibility: VisibilityType,

    // board 相关
    public readonly boardId?: string, // 保持兼容性
    private readonly _boardItemInfo?: BoardItemInfo, // 保持兼容性
    public position?: BoardPositionInfo, // 修正：构造时可选的位置信息
  ) {
    super();
  }

  static async create(
    param: CreateSnipParam,
    positionService?: BoardPositionDomainService,
  ): Promise<Snip> {
    const id = param.id || uuidv7();
    const now = new Date();

    Snip.logger.debug(
      `Creating snip: ${JSON.stringify(param)}, positionService = ${positionService}`,
    );

    // 如果指定了boardId，自动分配位置
    let position: BoardPositionInfo | undefined;
    if (param.boardId && positionService) {
      position = await positionService.assignPositionForNewEntity('snip', id, param.boardId, {
        parentBoardGroupId: param.parentBoardGroupId,
      });
    }

    Snip.logger.debug(`Creating snip - position: ${JSON.stringify(position)}`);

    const snip = new Snip(
      id,
      now,
      now,
      param.creatorId,
      param.spaceId,
      param.parentId,
      param.title,
      param.extra,
      param.type,
      param.from,
      param.status || null,
      param.visibility || 'private',
      param.boardId, // 保持兼容性
      undefined, // boardItemInfo will be generated from position
      position,
    );

    Snip.logger.debug(`new snip - position: ${JSON.stringify(snip.position)}`);

    snip.isNew = true;
    snip.apply(
      new SnipCreatedEvent(
        snip.id,
        snip.spaceId,
        snip.creatorId,
        snip.parentId || '',
        snip.title,
        snip.type,
        snip.from,
        snip.status || SnipStatus.FETCHING,
        snip.visibility,
      ),
    );

    return snip;
  }

  update(param: UpdateSnipParam): void {
    const hasChanges =
      param.title !== undefined ||
      param.status !== undefined ||
      param.visibility !== undefined ||
      param.extra !== undefined;

    if (!hasChanges) {
      return;
    }

    if (param.title !== undefined) {
      this.title = param.title;
    }

    if (param.status !== undefined) {
      this.status = param.status;
    }

    if (param.visibility !== undefined) {
      this.visibility = param.visibility;
    }

    if (param.extra !== undefined) {
      this.extra = param.extra;
    }

    this.updatedAt = new Date();

    this.apply(
      new SnipUpdatedEvent(
        this.id,
        this.spaceId,
        this.creatorId,
        this.title,
        this.type,
        this.status || SnipStatus.FETCHING,
        this.visibility,
      ),
    );
  }

  delete(): void {
    this.apply(new SnipDeletedEvent(this.id, this.spaceId, this.creatorId));
  }

  /**
   * 保存实体 - 根据是否为新实体决定创建或更新
   * 注意：实际的持久化操作应该在 Repository 层处理
   * 这里只负责应用相应的领域事件
   */
  save(): void {
    if (this.isNew) {
      // 对于新创建的实体，事件已经在 create 方法中应用了
      // 这里只需要标记为非新实体
      this.markAsExisting();
    } else {
      // 对于已存在的实体，应用更新事件
      this.apply(
        new SnipUpdatedEvent(
          this.id,
          this.spaceId,
          this.creatorId,
          this.title,
          this.type,
          this.status || SnipStatus.FETCHING,
          this.visibility,
        ),
      );
    }
  }

  publish(): void {
    if (this.visibility === 'public') {
      return;
    }

    this.visibility = 'public';
    this.updatedAt = new Date();

    this.apply(
      new SnipUpdatedEvent(
        this.id,
        this.spaceId,
        this.creatorId,
        this.title,
        this.type,
        this.status || SnipStatus.FETCHING,
        this.visibility,
      ),
    );
  }

  unpublish(): void {
    if (this.visibility === 'private') {
      return;
    }

    this.visibility = 'private';
    this.updatedAt = new Date();

    this.apply(
      new SnipUpdatedEvent(
        this.id,
        this.spaceId,
        this.creatorId,
        this.title,
        this.type,
        this.status || SnipStatus.FETCHING,
        this.visibility,
      ),
    );
  }

  /**
   * 移动到新位置 - 新增功能，支持混排
   */
  async moveTo(
    targetBoardId: string,
    options: {
      afterItemId?: string;
      beforeItemId?: string;
      parentBoardGroupId?: string;
    },
    positionService: BoardPositionDomainService,
  ): Promise<void> {
    const oldPosition = this.position;

    // 计算新位置
    this.position = await positionService.assignPositionForNewEntity(
      'snip',
      this.id,
      targetBoardId,
      options,
    );

    this.updatedAt = new Date();

    // 可以添加移动事件
    // this.apply(new SnipMovedEvent({
    //   snipId: this.id,
    //   fromPosition: oldPosition,
    //   toPosition: this.position
    // }));
  }

  /**
   * 设置位置信息 - 用于Repository加载数据时
   */
  setPosition(position: BoardPositionInfo): void {
    this.position = position;
  }

  get boardItemInfo(): BoardItemInfo | undefined {
    // 如果有新的位置信息，使用它生成boardItemInfo
    if (this.position) {
      return {
        id: this.position.boardItemId,
        boardId: this.position.boardId,
        parentBoardGroupId: this.position.parentBoardGroupId,
        boardGroupId: this.position.parentBoardGroupId, // API兼容性
        rank: this.position.rank,
        createdAt: this.createdAt,
        updatedAt: this.updatedAt,
      };
    }

    // 回退到原有的boardItemInfo（兼容性）
    return this._boardItemInfo;
  }

  // 兼容性方法，保持 API 接口一致
  get boardItem(): BoardItemInfo | undefined {
    return this.boardItemInfo;
  }

  /**
   * 新增：位置相关的便捷访问器
   */
  get rank(): string | undefined {
    return this.position?.rank;
  }

  get parentBoardGroupId(): string | undefined {
    return this.position?.parentBoardGroupId;
  }

  markAsExisting(): void {
    this.isNew = false;
  }

  /**
   * 更新状态
   */
  updateStatus(status: SnipStatus | null): void {
    this.status = status;
    this.updatedAt = new Date();

    this.apply(
      new SnipUpdatedEvent(
        this.id,
        this.spaceId,
        this.creatorId,
        this.title,
        this.type,
        this.status || SnipStatus.FETCHING,
        this.visibility,
      ),
    );
  }

  /**
   * 替换图片 URL（默认实现，子类可重写）
   * 注意：不同子类可能有不同的实现签名，这里只提供基础实现
   */
  replaceImageUrls(imageMapping: any): void {
    // 基类默认实现为空，子类可以重写此方法
    // 例如 Image 类型的 Snip 会重写此方法来处理图片 URL 替换
    console.debug(
      `replaceImageUrls called on base Snip class for ${this.id}, imageMapping:`,
      imageMapping,
    );
  }

  /**
   * clone 的时候，标记为来自模板
   */
  markAsTemplate(): void {
    const currentExtra = SafeParse(this.extra, false, {});
    (currentExtra as any).fromTemplate = true;
    this.extra = JSON.stringify(currentExtra);
  }

  updateBoardItemId(boardItemId: string): void {
    if (this.position) {
      this.position.boardItemId = boardItemId;
    }
  }
}
