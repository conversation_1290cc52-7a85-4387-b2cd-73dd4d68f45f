import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { TextRunnerService } from '@/modules/ai/runners/service/text-runner.service';
import { BoardRepository } from '@/modules/material-mng/repositories/board.repository';
import { BoardPositionDomainService } from '../../../domain/shared/board-position.service';
import { Thought, TitleType } from '../../../domain/thought/models/thought.entity';
import { ThoughtDto } from '../../../dto/thought.dto';
import { ThoughtRepository } from '../../../repositories/thought.repository';
import { CreateThoughtCommand } from '../../commands/thought/create-thought.command';
import { ThoughtDtoService } from '../../dto-services/thought-dto.service';

@CommandHandler(CreateThoughtCommand)
export class CreateThoughtHandler implements ICommandHandler<CreateThoughtCommand> {
  constructor(
    private readonly thoughtRepository: ThoughtRepository,
    private readonly boardPositionService: BoardPositionDomainService,
    private readonly thoughtDtoService: ThoughtDtoService,
    private readonly boardRepository: BoardRepository,
    private readonly textRunnerService: TextRunnerService,
  ) {}

  async execute(command: CreateThoughtCommand): Promise<ThoughtDto> {
    const { spaceId, creatorId, content, boardId, genTitle, parentBoardGroupId } = command;

    let title = command.title;
    let titleType: TitleType = TitleType.MANUAL;
    if (!title) {
      if (genTitle && content.plain) {
        const runner = this.textRunnerService.getThoughtAutoGenerateTitleRunner({
          text: content.plain,
        });
        const result = await runner.generateOnce();
        title = result.text;
      } else {
        title = 'New thought';
        titleType = TitleType.DEFAULT;
      }
    }

    const board = boardId
      ? await this.boardRepository.getById(boardId)
      : await this.boardRepository.getDefaultBoardBySpaceId(spaceId);
    // 使用新的异步创建方法
    const thought = await Thought.create(
      {
        spaceId,
        creatorId,
        title,
        titleType,
        content,
        boardId: board.id,
        parentBoardGroupId,
      },
      this.boardPositionService,
    ); // 如果有boardId，传入位置服务

    // 保存想法（包括 board item 关联）
    await this.thoughtRepository.save(thought);

    thought.commit();

    return this.thoughtDtoService.toDto(thought);
  }
}
