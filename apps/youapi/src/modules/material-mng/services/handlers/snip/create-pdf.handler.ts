/**
 * Create PDF Handler - 创建PDF文档处理器
 * 从 youapp 迁移的创建PDF文档处理器
 *
 * Migrated from:
 * - /youapp/src/app/api/v1/createPDF/route.ts
 */

import { BadRequestException, Logger } from '@nestjs/common';
import { CommandBus, CommandHandler, EventBus, ICommandHandler } from '@nestjs/cqrs';
import { LanguageEnum, QuotaResourceEnum } from '@/common/types';
import { getCallbackOrigin } from '@/common/utils/callback';
import { ChatDomainService } from '@/domain/chat';
import { FileDomainService } from '@/domain/file';
import { SpaceDomainService } from '@/domain/space';
import { UsageRecordDomainService } from '@/domain/usage-record';
import { Youget } from '@/infra/youget';
import { ReaderHTMLContent } from '@/modules/material-mng/domain/snip/value-objects/content.vo';
import { PdfDto } from '@/modules/material-mng/dto/snip/pdf.dto';
import { BlockRepository } from '@/modules/material-mng/repositories/block.repository';
import { BoardRepository } from '@/modules/material-mng/repositories/board.repository';
import { ContentRepository } from '@/modules/material-mng/repositories/content.repository';
import { BoardPositionDomainService } from '../../../domain/shared/board-position.service';
import { PDF } from '../../../domain/snip/models/pdf.entity';
import { UploadFileMeta } from '../../../domain/snip/models/type';
import { SnipRepository } from '../../../repositories/snip.repository';
import { CreatePdfCommand } from '../../commands/snip/create-pdf.command';
import { SnipDtoService } from '../../dto-services/snip-dto.service';

@CommandHandler(CreatePdfCommand)
export class CreatePdfHandler implements ICommandHandler<CreatePdfCommand> {
  private static readonly logger = new Logger(CreatePdfHandler.name);

  constructor(
    private readonly snipRepository: SnipRepository,
    private readonly boardPositionService: BoardPositionDomainService,
    private readonly snipDtoService: SnipDtoService,
    private readonly eventBus: EventBus,
    private readonly boardRepository: BoardRepository,
    private readonly youget: Youget,
    private readonly blockRepository: BlockRepository,
    private readonly contentRepository: ContentRepository,
    private readonly chatDomainService: ChatDomainService,
    private readonly commandBus: CommandBus,
    private readonly spaceDomainService: SpaceDomainService,
    private readonly usageRecordDomainService: UsageRecordDomainService,
    private readonly fileDomainService: FileDomainService,
  ) {}

  async execute(command: CreatePdfCommand): Promise<PdfDto> {
    const {
      spaceId,
      creatorId,
      file,
      title,
      authors,
      publishedAt,
      heroImageUrl,
      extra,
      syncTranscribe,
      boardId: originalBoardId,
      parentBoardGroupId,
      chatId,
    } = command;

    // 验证必填字段
    if (!file) {
      throw new BadRequestException('File is required for PDF creation');
    }

    // 检查配额
    const space = await this.spaceDomainService.get(spaceId);
    if (space) {
      await this.usageRecordDomainService.checkQuota(space, QuotaResourceEnum.STORAGE);
    }

    // 确定 boardId：如果指定了则使用，否则获取默认 board
    let boardId: string;
    if (originalBoardId) {
      await this.boardRepository.getById(originalBoardId);
      boardId = originalBoardId;
    } else {
      boardId = (await this.boardRepository.getDefaultBoardBySpaceId(spaceId)).id;
    }

    // 获取文件元信息
    const fileMeta = await this.fileDomainService.getFileMeta(
      file.name,
      file.hash,
      file.isPublic ? 'public' : 'private',
    );

    // 创建PDF实体，与 youapp 保持一致
    const pdf = await PDF.create(
      {
        spaceId,
        creatorId,
        title: title || this.extractTitleFromFile(file.name),
        file: <UploadFileMeta>{
          name: fileMeta.name,
          mimeType: fileMeta.mime_type,
          size: fileMeta.size,
          storageUrl: fileMeta.storage_url,
        },
        authors,
        publishedAt,
        heroImageUrl,
        extra,
        boardId,
        parentBoardGroupId,
      },
      boardId ? this.boardPositionService : undefined,
    );

    await this.snipRepository.save(pdf);

    CreatePdfHandler.logger.debug(`before save PDF: ${JSON.stringify(pdf)}`);

    const transcribeParams = {
      file: {
        url: pdf.file.storageUrl,
      },
      youget_callback_url: `${getCallbackOrigin()}/webhook/v1/pdf-parsed?snip_id=${pdf.id}`,
      youget_callback_method: 'PUT',
    };
    if (syncTranscribe) {
      const result = await this.youget.executeTranscribePdfTaskInSync(transcribeParams, creatorId);
      if (result) {
        pdf.content = new ReaderHTMLContent(
          result.content.raw,
          result.content.plain,
          result.content.language as LanguageEnum,
        );
        await pdf.save();
      }
    } else {
      this.youget.executeTranscribePdfTaskInBackground(transcribeParams, creatorId);
    }

    // 绑定聊天 - 与 youapp 保持一致
    if (chatId && pdf.id) {
      await this.chatDomainService.bindWebpageChatToSnip({
        snip_id: pdf.id!,
        chat_id: chatId,
      });
    }

    CreatePdfHandler.logger.debug(`PDF snip created successfully: ${pdf.id}`);

    // 返回 DTO，与 youapp 的 snip2vo 功能保持一致
    const dto = this.snipDtoService.toDto(pdf);

    return {
      ...dto,
      boardIds: boardId ? [boardId] : [], // 兼容旧数据，实际取 boardId 即可
    };
  }

  /**
   * 从文件名提取标题
   */
  private extractTitleFromFile(fileName: string): string {
    if (fileName) {
      // 移除文件扩展名
      return fileName.replace(/\.[^/.]+$/, '');
    }
    return 'PDF Document';
  }

  /**
   * 启动PDF解析任务
   */
  startPdfParsingInBackground(snipId: string, creatorId: string, storageUrl: string) {
    CreatePdfHandler.logger.debug('Starting PDF parsing task in background');

    // TODO: 实现PDF解析逻辑
    // 参考 youapp 中的PDF解析实现
    this.youget.executeExtractTextTaskInBackground(
      {
        file: {
          url: storageUrl,
        },
        youget_callback_url: `${getCallbackOrigin()}/webhook/v1/pdf-parsed?snip_id=${snipId}`,
        youget_callback_method: 'PUT',
      },
      creatorId,
    );
  }
}
