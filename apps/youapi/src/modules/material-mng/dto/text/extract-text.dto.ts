import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsOptional, IsString, IsUrl } from 'class-validator';
import type { ApiV1ShortcutCreateAndRunExtractTextTaskPostDefaultResponseResult } from '@/infra/youget/typescript-axios-client/api';

export class ExtractTextRequestDto {
  @ApiProperty({
    description: '图片的URL地址',
    example: 'https://example.com/image.jpg',
  })
  @IsString()
  @IsUrl()
  url: string;

  @ApiProperty({
    description: '是否自动调整图片，会显著增加处理耗时',
    example: false,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  adaptImage?: boolean;
}

export class ExtractTextResponseDto {
  @ApiProperty({
    description: '提取的文本结果',
    example: 'This is the extracted text from the image',
  })
  result: ApiV1ShortcutCreateAndRunExtractTextTaskPostDefaultResponseResult;
}
