import { ApiProperty } from '@nestjs/swagger';
import { EntityTypeEnum } from '@/common/types';
import { SnipDto } from './snip/snip.dto';
import { ThoughtDto } from './thought.dto';

// export class GetSharedEntityDto {
//   @ApiProperty({ description: '短链接ID', example: 'Abc123Def456' })
//   @IsString()
//   shortId: string;
// }

export class GetSharedEntityResponseDto {
  @ApiProperty({
    description: '实体类型',
    enum: EntityTypeEnum,
    example: EntityTypeEnum.THOUGHT,
  })
  entityType: EntityTypeEnum;

  @ApiProperty({
    description: '实体信息',
    oneOf: [
      { $ref: '#/components/schemas/ThoughtDto' },
      { $ref: '#/components/schemas/ArticleDto' },
      { $ref: '#/components/schemas/OfficeDto' },
      { $ref: '#/components/schemas/ImageDto' },
      { $ref: '#/components/schemas/VideoDto' },
      { $ref: '#/components/schemas/OtherWebpageDto' },
      { $ref: '#/components/schemas/PdfDto' },
      { $ref: '#/components/schemas/TextDto' },
      // { $ref: '#/components/schemas/UnknownWebpageDto' }, 会导致 SDK 生成出错，因为 UnknownWebpageDto 在 schema 里未被定义
      { $ref: '#/components/schemas/VoiceDto' },
      { $ref: '#/components/schemas/SnippetDto' },
    ],
  })
  entity: ThoughtDto | SnipDto;
}
