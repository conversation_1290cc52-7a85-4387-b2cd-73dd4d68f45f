/**
 * Base Prompt Service - 基础提示服务
 * 提供基础的提示获取和消息构建功能
 *
 * Migrated from:
 * - apps/youapi/src/infra/youllm/prompt/index.ts (YouPrompt class)
 */

import { Injectable, Logger } from '@nestjs/common';
import type { CoreMessage, TextPart } from 'ai';
import { ChatPromptClient, type LangfusePromptClient } from 'langfuse-core';
import { BadPromptError } from '@/common/errors';
import { LangfuseTraceService } from '@/common/services/langfuse-trace.service';
import { type ArrayModelMessage } from '@/common/types';
import * as fallbackPrompts from './fallback/index';

/**
 * Langfuse compiled message interface
 */
export interface LangfuseCompiledMessage {
  role: string;
  content: string;
}

interface LocalPrompts {
  [key: string]: Pick<LangfusePromptClient, 'prompt' | 'config' | 'name' | 'version'>;
}

export interface FetchPromptParams {
  name: string;
  variables?: Record<string, any>;
}

@Injectable()
export class PromptService {
  protected readonly logger = new Logger(PromptService.name);

  constructor(private readonly traceService: LangfuseTraceService) {}

  fetchPrompt(param: FetchPromptParams): ChatPromptClient {
    const { name } = param;
    const prompt = (fallbackPrompts as unknown as LocalPrompts)[name.replace(/-/g, '')];

    if (!prompt) {
      this.traceService.addEvent({
        name: 'prompt-not-found',
        level: 'WARNING',
        input: { prompt: name },
      });
      throw new BadPromptError({ prompt: name });
    }

    const options = {
      name,
      version: prompt.version,
      config: prompt.config,
      labels: ['local'],
      tags: [],
    };

    if (typeof prompt.prompt === 'string') {
      return new ChatPromptClient({
        ...options,
        type: 'chat',
        prompt: [
          {
            role: 'system',
            content: prompt.prompt,
          },
        ],
      });
    }

    // Check if prompt.prompt is an array of messages (which Langfuse doesn't support)
    if (Array.isArray(prompt.prompt) && prompt.prompt.length > 0) {
      // When prompt.prompt is an array of messages, Langfuse doesn't support this format
      // Create an empty ChatPromptClient and then we'll use the messages directly
      const emptyPrompt = new ChatPromptClient({
        ...options,
        type: 'chat',
        prompt: [
          {
            role: 'system',
            content: '',
          },
        ],
      });

      // Store the original messages in a custom property so we can access them later
      (emptyPrompt as any)._originalMessages = prompt.prompt;

      return emptyPrompt;
    }

    return new ChatPromptClient({
      ...options,
      type: 'chat',
      prompt: prompt.prompt,
    });
  }

  /**
   * Compile a prompt and convert it directly to AI SDK format
   *
   * @param prompt - The Langfuse chat prompt client
   * @param variables - Variables to substitute in the prompt
   * @returns Array of ModelMessages compatible with AI SDK
   */
  getPromptAndMessages(
    promptName: string,
    variables: Record<string, any> = {},
  ): {
    prompt: ChatPromptClient;
    promptMessages: CoreMessage[];
  } {
    const prompt = this.fetchPrompt({
      name: promptName,
      variables,
    });
    // First compile to our array-only format, then convert to AI SDK format
    const arrayMessages = this.compileToArray(prompt, variables);
    const messages = this.convertArrayToAISDK(arrayMessages);
    return {
      prompt,
      promptMessages: messages,
    };
  }

  /**
   * Compile a prompt to our array-only message format
   * All content is arrays, providing consistency in internal handling
   *
   * @param prompt - The Langfuse chat prompt client
   * @param variables - Variables to substitute in the prompt
   * @returns Array of ArrayModelMessages with consistent array content
   */
  private compileToArray(
    prompt: ChatPromptClient,
    variables: Record<string, any> = {},
  ): ArrayModelMessage[] {
    // Check if we have original messages stored (for array prompts)
    if ((prompt as any)._originalMessages) {
      const originalMessages = (prompt as any)._originalMessages;

      // Process the original messages directly
      const messages: ArrayModelMessage[] = originalMessages.map(
        (message: any): ArrayModelMessage => {
          const role = message.role as 'system' | 'user' | 'assistant';

          // Ensure content is always an array
          const content = Array.isArray(message.content)
            ? message.content
            : [{ type: 'text', text: message.content }];

          return {
            role,
            content,
          };
        },
      );

      // Now do variable replacement on any text content within each message part
      return messages.map((message) => ({
        ...message,
        content: message.content.map((part) => {
          return this.replaceVariablesInPart(part, variables);
        }),
      }));
    }

    // First compile prompt without variables to get raw structure
    const rawResult = prompt.compile({});

    let messages: ArrayModelMessage[] = [];

    if (typeof rawResult === 'string') {
      // TextPromptClient - convert to system message with array content
      messages = [
        {
          role: 'system',
          content: [{ type: 'text', text: rawResult }],
        },
      ];
    } else if (Array.isArray(rawResult)) {
      // ChatPromptClient - format each message with consistent array content
      messages = rawResult.map((message: LangfuseCompiledMessage): ArrayModelMessage => {
        const role = message.role as 'system' | 'user' | 'assistant';

        switch (role) {
          case 'system':
            return {
              role: 'system',
              content: [{ type: 'text', text: message.content }],
            };
          case 'user':
            return {
              role: 'user',
              content: [{ type: 'text', text: message.content }],
            };
          case 'assistant':
            return {
              role: 'assistant',
              content: [{ type: 'text', text: message.content }],
            };
          default:
            // Default to user if role is unexpected
            return {
              role: 'user',
              content: [{ type: 'text', text: message.content }],
            };
        }
      });
    }

    // Now do variable replacement on any text content within each message part
    return messages.map((message) => ({
      ...message,
      content: message.content.map((part) => {
        return this.replaceVariablesInPart(part, variables);
      }),
    }));
  }

  /**
   * Replace variables in any message part, handling different part types
   */
  private replaceVariablesInPart(part: any, variables: Record<string, any>): any {
    // Handle text parts
    if (part.type === 'text' && typeof part.text === 'string') {
      return {
        ...part,
        text: this.replaceVariables(part.text, variables),
      };
    }

    // Handle image parts (alt text, etc.)
    if (part.type === 'image' && typeof part.alt === 'string') {
      return {
        ...part,
        alt: this.replaceVariables(part.alt, variables),
      };
    }

    // Handle tool parts (name, description, etc.)
    if (part.type === 'tool') {
      const updatedPart = { ...part };
      if (typeof part.name === 'string') {
        updatedPart.name = this.replaceVariables(part.name, variables);
      }
      if (typeof part.description === 'string') {
        updatedPart.description = this.replaceVariables(part.description, variables);
      }
      return updatedPart;
    }

    // For any other part type, recursively check all string properties
    const updatedPart = { ...part };
    Object.keys(updatedPart).forEach((key) => {
      if (typeof updatedPart[key] === 'string') {
        updatedPart[key] = this.replaceVariables(updatedPart[key], variables);
      } else if (typeof updatedPart[key] === 'object' && updatedPart[key] !== null) {
        // Recursively handle nested objects
        updatedPart[key] = this.replaceVariablesInObject(updatedPart[key], variables);
      }
    });

    return updatedPart;
  }

  /**
   * Recursively replace variables in nested objects
   */
  private replaceVariablesInObject(obj: any, variables: Record<string, any>): any {
    if (Array.isArray(obj)) {
      return obj.map((item) => this.replaceVariablesInObject(item, variables));
    }

    if (typeof obj === 'object' && obj !== null) {
      const result = { ...obj };
      Object.keys(result).forEach((key) => {
        if (typeof result[key] === 'string') {
          result[key] = this.replaceVariables(result[key], variables);
        } else if (typeof result[key] === 'object' && result[key] !== null) {
          result[key] = this.replaceVariablesInObject(result[key], variables);
        }
      });
      return result;
    }

    return obj;
  }

  /**
   * Replace variables in text content
   * Handles {{variable}} syntax and escapes existing braces
   */
  private replaceVariables(text: string, variables: Record<string, any>): string {
    let result = text;

    // Replace variables
    Object.keys(variables).forEach((key) => {
      if (variables[key] != null) {
        const value = String(variables[key]);
        // Escape {{ and }} in the replacement value to avoid conflicts
        const escapedValue = value.replace(/{{/g, '&#123;&#123;').replace(/}}/g, '&#125;&#125;');
        result = result.replace(new RegExp(`{{${key}}}`, 'g'), escapedValue);
      }
    });

    return result;
  }

  /**
   * Convert array-only messages to AI SDK CoreMessage format
   * Handles the different content requirements for different role types in AI SDK
   *
   * @param arrayMessages - Array of array-only messages
   * @returns Array of ModelMessages compatible with AI SDK
   */
  private convertArrayToAISDK(arrayMessages: ArrayModelMessage[]): CoreMessage[] {
    return arrayMessages.map((message): CoreMessage => {
      switch (message.role) {
        case 'system': {
          // System messages in AI SDK expect string content
          const textContent = message.content
            .filter((c): c is TextPart => c.type === 'text')
            .map((c) => c.text)
            .join('\n');

          return {
            role: 'system',
            content: textContent,
          };
        }

        case 'user': {
          // User messages in AI SDK expect array content
          // Convert OpenAI-style image_url format to AI SDK image format
          const convertedContent = message.content.map((part: any) => {
            if (part.type === 'image_url' && part.image_url?.url) {
              return {
                type: 'image' as const,
                image: part.image_url.url,
              };
            }
            return part;
          });

          return {
            role: 'user',
            content: convertedContent,
          };
        }

        case 'assistant':
          // Assistant messages in AI SDK expect array content
          return {
            role: 'assistant',
            content: message.content as any,
          };

        default:
          throw new Error(`Invalid message role: ${message.role}`);
      }
    });
  }
}
