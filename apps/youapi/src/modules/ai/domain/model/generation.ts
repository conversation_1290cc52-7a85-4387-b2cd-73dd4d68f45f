import { Logger } from '@nestjs/common';
import { AggregateRoot } from '@nestjs/cqrs';
import { CoreMessage, Tool, ToolChoice } from 'ai';
import { ChatPromptClient, LangfuseGenerationClient } from 'langfuse';
import { ChatCompletionTool } from 'openai/resources/index.js';
import { DEFAULT_AI_CHAT_MODEL, GenerationStatusEnum, LLMs } from '@/common/types';

export interface ImageEditMask {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface ImageEditOptions {
  prompt: string;
  size?: '1024x1024' | '1536x1024' | '1024x1536' | 'auto';
  quality?: 'standard' | 'hd';
  mask?: ImageEditMask[];
}

export interface ImageGenerationOptions {
  prompt: string;
  size?: '256x256' | '512x512' | '1024x1024' | '1792x1024' | '1024x1792';
  quality?: 'standard' | 'hd';
  style?: 'vivid' | 'natural';
  n?: number;
}

export class Generation extends AggregateRoot {
  protected readonly logger = new Logger(Generation.name);

  public model: LLMs;
  public temperature: number;
  public tools: ChatCompletionTool[];
  public toolChoice: ToolChoice<Tool>;
  public modelOptions: Record<string, any>;
  public traceMetadata: Record<string, string>;
  public bizArgs: Record<string, any>;

  protected status: GenerationStatusEnum = GenerationStatusEnum.PENDING;
  protected trace: LangfuseGenerationClient | null = null;

  public promptName: string;
  public promptVersion: number;
  public promptMessages: CoreMessage[];

  constructor(param: {
    model?: LLMs;
    temperature?: number;
    tools?: ChatCompletionTool[];
    toolChoice?: ToolChoice<Tool>;
    bizArgs?: Record<string, any>;
    modelOptions?: Record<string, any>;
    traceMetadata?: Record<string, string>;
    prompt?: ChatPromptClient;
    promptMessages?: CoreMessage[];
  }) {
    super();

    const promptConfig = param.prompt?.config as {
      model?: LLMs;
      temperature?: number;
      topP?: number;
      frequencyPenalty?: number;
      presencePenalty?: number;
    };

    this.model = param.model || promptConfig?.model || DEFAULT_AI_CHAT_MODEL;
    this.temperature = param.temperature || promptConfig?.temperature || 0.5;
    this.tools = param.tools || [];
    this.toolChoice = param.toolChoice || this.tools.length > 0 ? 'auto' : 'none';

    const topP = param.modelOptions?.topP || promptConfig?.topP || 1;
    const frequencyPenalty =
      param.modelOptions?.frequencyPenalty || promptConfig?.frequencyPenalty || 0;
    const presencePenalty =
      param.modelOptions?.presencePenalty || promptConfig?.presencePenalty || 0;

    this.bizArgs = param.bizArgs || {};
    this.traceMetadata = param.traceMetadata || {};
    this.modelOptions = {
      ...param.modelOptions,
      ...(topP ? { topP } : {}),
      ...(frequencyPenalty ? { frequencyPenalty } : {}),
      ...(presencePenalty ? { presencePenalty } : {}),
    };
    this.promptName = param.prompt?.name || '';
    this.promptVersion = param.prompt?.version || 1;
    this.promptMessages = param.promptMessages || [];
  }

  public setStatus(status: GenerationStatusEnum) {
    this.status = status;
  }
}

// export class ImageGeneration extends Generation {
//   public imageOptions: ImageGenerationOptions | null = null;
//   public imageEditOptions: ImageEditOptions | null = null;
//   public imageUrl: string | null = null;
//   public operation: 'generate' | 'edit' = 'generate';

//   constructor(param: {
//     model?: LLMs;
//     temperature?: number;
//     traceMetadata?: Record<string, string>;
//     prompt?: ChatPromptClient;
//     imageOptions?: ImageGenerationOptions;
//     imageEditOptions?: ImageEditOptions;
//     imageUrl?: string;
//     operation?: 'generate' | 'edit';
//   }) {
//     super({
//       model: param.model || LLMs.GPT_IMAGE_1,
//       temperature: param.temperature || 0,
//       traceMetadata: param.traceMetadata,
//       prompt: param.prompt,
//     });

//     this.imageOptions = param.imageOptions || null;
//     this.imageEditOptions = param.imageEditOptions || null;
//     this.imageUrl = param.imageUrl || null;
//     this.operation = param.operation || 'generate';
//   }

//   static createForGeneration(
//     options: ImageGenerationOptions,
//     param?: {
//       traceMetadata?: Record<string, string>;
//       prompt?: ChatPromptClient;
//     },
//   ): ImageGeneration {
//     return new ImageGeneration({
//       ...param,
//       imageOptions: options,
//       operation: 'generate',
//     });
//   }

//   static createForEdit(
//     imageUrl: string,
//     options: ImageEditOptions,
//     param?: {
//       traceMetadata?: Record<string, string>;
//       prompt?: ChatPromptClient;
//     },
//   ): ImageGeneration {
//     return new ImageGeneration({
//       ...param,
//       imageUrl,
//       imageEditOptions: options,
//       operation: 'edit',
//     });
//   }
// }
