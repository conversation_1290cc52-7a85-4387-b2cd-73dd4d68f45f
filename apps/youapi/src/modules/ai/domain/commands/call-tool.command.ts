import { ObjectStreamPart, TextStreamPart, ToolSet } from 'ai';
import { ChatCompletionTool } from 'openai/resources/index.js';
import { Subject } from 'rxjs';
import { ToolCompletionBlock } from '@/modules/chat/domain/message/models/completion-block.vo';
import { Generation } from '../model/generation';

export class CallToolCommand<T extends ToolSet = any> {
  constructor(
    public readonly param: {
      input: Record<string, any>;
      subject: Subject<TextStreamPart<T> | ObjectStreamPart<T>>;
      generation: Generation;
      toolDefinition: ChatCompletionTool;
      completionBlock: ToolCompletionBlock;
    },
  ) {}
}
