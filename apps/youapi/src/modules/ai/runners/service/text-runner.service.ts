import { Injectable } from '@nestjs/common';
import { TextRunner } from '../text';

@Injectable()
export class TextRunnerService {
  getSuggestionRunner(variables: Record<string, any>): TextRunner {
    return TextRunner.fromPrompt('suggestion-prompt', variables);
  }

  getGenerateTitleRunner(variables: Record<string, any>): TextRunner {
    return TextRunner.fromPrompt('generate-title-prompt', variables);
  }

  getThoughtAutoGenerateTitleRunner(variables: Record<string, any>): TextRunner {
    return TextRunner.fromPrompt('thought-auto-generate-title-prompt', variables);
  }

  getExplainRunner(variables: Record<string, any>): TextRunner {
    return TextRunner.fromPrompt('explain-prompt', variables);
  }

  getSelfDescByFeedsRunner(variables: Record<string, any>): TextRunner {
    return TextRunner.fromPrompt('self-desc-by-feeds-prompt', variables);
  }

  getGeneratePunctuationRunner(variables: Record<string, any>): TextRunner {
    return TextRunner.fromPrompt('subtitle-punctuation-prompt', variables);
  }

  getDetectSpeakersByTranscriptRunner(variables: Record<string, any>): TextRunner {
    return TextRunner.fromPrompt('detect-speakers-by-transcript-prompt', variables);
  }

  getAudioOverviewRunner(variables: Record<string, any>): TextRunner {
    return TextRunner.fromPrompt('audio-overview-prompt', variables);
  }

  getAskRunner(variables: Record<string, any>): TextRunner {
    return TextRunner.fromPrompt('ai-ask-chat-prompt', variables);
  }

  getQueryAnalyzeRunner(variables: Record<string, any>): TextRunner {
    return TextRunner.fromPrompt('query-analyze-prompt', variables);
  }

  getSVGDiagramRunner(variables: Record<string, any>): TextRunner {
    return TextRunner.fromPrompt('svg-generator-prompt', variables);
  }

  getEntityNameRunner(variables: Record<string, any>): TextRunner {
    return TextRunner.fromPrompt('extract-entity-names', variables);
  }
}
