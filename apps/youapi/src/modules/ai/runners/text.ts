import { Logger } from '@nestjs/common';
import {
  GenerateTextResult,
  generateText,
  StreamTextResult,
  streamText,
  TextStreamPart,
  ToolSet,
} from 'ai';
import { LangfuseGenerationClient } from 'langfuse-core';
import { merge, Subject } from 'rxjs';
import { ApplicationContext } from '@/common/utils/application-context';
import { Generation } from '../domain/model/generation';
import { PromptService } from '../prompt/index.service';
import { ModelProviderService } from '../providers/index.service';
import { streamToObservable } from '../utils/toObservable';
import { BaseRunner, type GenerateOptions } from './base';

/**
 * TextR<PERSON><PERSON> wraps the Generation domain model with execution logic
 * Tracing is handled by decorators and subscriptions
 */
export class TextRunner extends BaseRunner {
  protected readonly logger = new Logger(TextRunner.name);
  protected promptService: PromptService;

  public async generateOnce(options?: GenerateOptions): Promise<GenerateTextResult<any, any>> {
    const { useCache = true } = options || {};
    if (!this.currentGeneration) {
      throw new Error('Generation is required');
    }

    const model = ApplicationContext.getProvider<ModelProviderService>(
      ModelProviderService,
    ).getLanguageModel(this.model, { useCache });
    const traceName = this.promptName || 'text-generation';
    await this.startTrace(model, traceName);

    // Start generation tracking - use prompt name if available
    try {
      const result = await generateText(
        this.getCallApiParams(model, new Subject<TextStreamPart<ToolSet>>()),
      );

      // End generation with success
      const usageReport = this.buildUsageReport(result.usage);
      await this.traceService.updateGeneration({
        ...usageReport,
        output: result.text,
        metadata: {
          finishReason: result.finishReason,
          success: true,
        },
      });

      return result;
    } catch (error) {
      await this.traceService.updateGeneration({
        metadata: {
          success: false,
          error: error.message,
          errorStack: error.stack,
        },
      });
      throw error;
    }
  }

  public async generateStream(options?: GenerateOptions) {
    const { useCache = true } = options || {};
    if (!this.currentGeneration) {
      throw new Error('Generation is required');
    }

    const model = ApplicationContext.getProvider<ModelProviderService>(
      ModelProviderService,
    ).getLanguageModel(this.model, { useCache });

    const traceName = this.promptName || 'stream-text-generation';
    const traceNode = await this.startTrace(model, traceName, options);

    const toolResponseSubject = new Subject<TextStreamPart<ToolSet>>();
    const generator = streamText({
      ...this.getCallApiParams(model, toolResponseSubject),
      onFinish: ({ usage, finishReason }) => {
        toolResponseSubject.complete();

        const usageReport = this.buildUsageReport(usage);
        traceNode.end({
          ...usageReport,
          metadata: {
            success: true,
            finishReason,
          },
        });
      },
      onError: ({ error }) => {
        toolResponseSubject.complete();

        traceNode.end({
          metadata: {
            success: false,
            error: error instanceof Error ? error.message : String(error),
            errorStack: error instanceof Error ? error.stack : undefined,
          },
        });
      },
    });

    // 合并 toolResponseSubject 与 generator，作为最后的消息流返回
    const mergedObservable = merge(
      toolResponseSubject.asObservable(),
      streamToObservable(generator),
    );
    return mergedObservable;
  }

  static fromPrompt(promptName: string, variables: Record<string, any>) {
    const promptService = ApplicationContext.getProvider<PromptService>(PromptService);
    const { prompt, promptMessages } = promptService.getPromptAndMessages(promptName, variables);
    const generation = new Generation({
      prompt,
      promptMessages,
    });
    const runner = new TextRunner();
    runner.addGeneration(generation);
    return runner;
  }
}
