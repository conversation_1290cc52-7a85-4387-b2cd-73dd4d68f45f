import type { Editor } from '@tiptap/core';
import { ImageUploadController } from '@/components/editor-kit/extensions/image-upload-controller';
import {
  InjectNativeMethodName,
  RegisterToNativeMethodName,
} from '../../editor-kit/extensions/native-message/const';
import { getNativeMessageControllerFromEditor } from '../../editor-kit/extensions/native-message/utils';
import type { MobileEditorExtensionOptions } from '../../editor-kit/thought-mobile-editor/mobile-editor-extension';

export const getMobileThoughtBodyExtensionOptions =
  (): MobileEditorExtensionOptions['extensionsOptions'] => {
    return {
      imageOptions: {
        downloadEnable: false,
        copyEnable: false,
        resizeEnable: false,
        imageUploadController: new ImageUploadController(),
        handleCopyImageUrl: (params: { editor: Editor; url: string }) => {
          const { editor, url } = params;
          const nativeMessageController = getNativeMessageControllerFromEditor(editor);
          navigator.clipboard.writeText(url).then(() => {
            nativeMessageController?.showToast({
              message: 'Copied image link',
            });
          });
        },
      },
      diffBlockOptions: {
        DiffBlockManageEnable: false,
      },
      linkOptions: {
        hoverMenuOptions: {
          openLink: {
            enable: true,
            handleOpenLink: (params: { editor: Editor; url: string }) => {
              const { editor, url } = params;
              const nativeMessageController = getNativeMessageControllerFromEditor(editor);
              nativeMessageController?.openLink({
                url,
              });
            },
          },
          copyLink: {
            enable: true,
            handleCopyLink: (params: { editor: Editor; url: string }) => {
              const { editor, url } = params;
              const nativeMessageController = getNativeMessageControllerFromEditor(editor);
              navigator.clipboard.writeText(url).then(() => {
                nativeMessageController?.showToast({
                  message: 'Copied to clipboard',
                });
              });
            },
          },
        },
      },
      nativeMessageExtensionOptions: {
        registerToNativeMethodEnableOptions: {
          [RegisterToNativeMethodName.GET_EDITOR_PATCH_DATA]: true,
          [RegisterToNativeMethodName.SET_EDITOR_PATCH_DATA]: true,
          [RegisterToNativeMethodName.INSERT_NODE]: true,
          [RegisterToNativeMethodName.SET_NODE_MARK]: true,
          [RegisterToNativeMethodName.INVOKE_REDO]: true,
          [RegisterToNativeMethodName.INVOKE_UNDO]: true,
          [RegisterToNativeMethodName.FOCUS_EDITOR]: true,
          [RegisterToNativeMethodName.COPY_SELECTION]: true,
          [RegisterToNativeMethodName.CUT_SELECTION]: true,
        },
        injectToNativeMethodEnableOptions: {
          [InjectNativeMethodName.EDITOR_IS_READY]: true,
          [InjectNativeMethodName.UPDATE_EDITOR_DATA]: true,
          [InjectNativeMethodName.SET_UNDO_OPTIONS]: true,
          [InjectNativeMethodName.SET_REDO_OPTIONS]: true,
          [InjectNativeMethodName.SHOW_TOAST]: true,
          [InjectNativeMethodName.ON_SELECTION_CHANGE]: true,
          [InjectNativeMethodName.ON_FOCUS]: true,
          [InjectNativeMethodName.ON_BLUR]: true,
          [InjectNativeMethodName.OPEN_LINK]: true,
        },
      },
    };
  };
