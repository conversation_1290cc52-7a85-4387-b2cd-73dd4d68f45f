import {
  PatchThoughtDto,
  PatchThoughtDtoTitleTypeEnum,
} from '@repo/api/generated-client/snake-case/models/PatchThoughtDto';
import { ThoughtVersionDto } from '@repo/api/generated-client/snake-case/models/ThoughtVersionDto';
import { DIFF_CHANGE_TYPE, diffTransformUtils } from '@repo/editor-common';
import type { Editor } from '@tiptap/core';
import { getText } from '@tiptap/core';
import { debounce, throttle } from 'lodash-es';
import type { RefObject } from 'react';
import type { IndexeddbPersistence } from 'y-indexeddb';
import type { Doc } from 'yjs';
import { apiClient } from '@/utils/callHTTP';
import { SelectionType } from '../const';
import type { ThoughtBodyComponentRef } from '../type';
import { ThoughtContentUtils } from './thought-content-utils';
import { MIN_GEN_TITLE_CONTENT_LENGTH, ThoughtTitleUtils } from './thought-title-utils';
import { ThoughtVersionUtils } from './thought-version-utils';

export interface BaseThoughtWorkflowOptions {
  id?: string;
  editor: Editor;
  ydoc: Doc;
  indexedDB?: IndexeddbPersistence | null;
  componentRef: RefObject<ThoughtBodyComponentRef>;
}

// 向服务端和向外部更新数据是 2s 一次
// 向外部更新标题数据时间会缩短一点，需要外部做一些配合，只更新标题
const THOUGHT_UPDATE_DATA_THROTTLE_TIME = 2000;

export abstract class BaseThoughtWorkflow {
  protected id?: string;
  protected editor: Editor;
  protected ydoc: Doc;
  protected indexedDB?: IndexeddbPersistence | null;
  protected componentRef: RefObject<ThoughtBodyComponentRef>;
  protected thoughtTitleUtils: ThoughtTitleUtils;
  protected executeUpdateTaskPromise: Promise<void> | null = null;
  protected shouldExecuteNext = false;
  protected versionUtils?: ThoughtVersionUtils;
  protected boundOnEditorUpdate: () => void;

  constructor(options: BaseThoughtWorkflowOptions) {
    this.id = options.id;
    this.editor = options.editor;
    this.ydoc = options.ydoc;
    this.indexedDB = options.indexedDB;
    this.componentRef = options.componentRef;

    this.thoughtTitleUtils = new ThoughtTitleUtils({
      id: this.id,
      editor: this.editor,
      thoughtComponentRef: this.componentRef,
    });

    if (this.id) {
      this.versionUtils = new ThoughtVersionUtils({
        id: this.id,
        editor: this.editor,
        ydoc: this.ydoc,
        componentRef: this.componentRef,
      });
    }

    this.boundOnEditorUpdate = this.onEditorUpdate.bind(this);
    this.init();
  }

  protected init() {
    if (this.id) {
      this.editor.on('update', this.boundOnEditorUpdate);
    }
  }

  protected onEditorUpdate() {
    if (!this.id) {
      return;
    }
    if (this.executeUpdateTaskPromise) {
      this.shouldExecuteNext = true;
      return;
    }
    this.executeUpdateTaskPromise = this.handleUpdate();
  }

  protected handleUpdate = throttle(async () => {
    const contentMarkdownData = ThoughtContentUtils.getMarkdownData(this.editor.state.doc);

    this.versionUtils?.trySaveAutoVersion(contentMarkdownData);

    const isNeedGenAITitle = this.thoughtTitleUtils.shouldGenAITitle();

    const aiTitleOrCurTitle = isNeedGenAITitle
      ? await this.thoughtTitleUtils.getAITitle({
          content: contentMarkdownData,
        })
      : this.title || '';

    const { title, titleType } = this.getComplexTitleAfterGenAITitle(
      aiTitleOrCurTitle,
      isNeedGenAITitle,
    );

    this.thoughtComponent?.setTitle(title);
    this.thoughtComponent?.setTitleType(titleType);

    const content = {
      raw: ThoughtContentUtils.getRawData(this.ydoc),
      plain: contentMarkdownData,
    };
    await this.callPatchThought(title, content, titleType);

    // 子类实现具体的逻辑
    // 向外更新的时候，一定是最新数据
    this.performUpdate({
      title: this.title,
      title_type: this.titleType,
      content,
    });

    this.afterTaskFinished();
  }, THOUGHT_UPDATE_DATA_THROTTLE_TIME);

  // 抽象方法，由子类实现具体的更新逻辑
  protected abstract performUpdate(data: Partial<PatchThoughtDto>): void;

  private async callPatchThought(
    title: string,
    content: PatchThoughtDto['content'],
    titleType: PatchThoughtDtoTitleTypeEnum,
  ) {
    const { error } = await apiClient.thoughtApi.patchThought(
      {
        id: this.id!,
        title,
        content,
        title_type: titleType,
      },
      {
        silent: true,
      },
    );
    // const { error } = await callHTTP('/api/v1/patchThought', {
    //   method: 'POST',
    //   body: {
    //     id: this.id,
    //     title,
    //     content,
    //     title_type: titleType,
    //   },
    //   silent: true,
    // });
    if (error) {
      this.handleFailed('Failed to update thought, please check your network');
    }
  }

  protected abstract handleFailed(description: string): void;

  protected afterTaskFinished() {
    this.executeUpdateTaskPromise = null;
    if (this.shouldExecuteNext) {
      this.shouldExecuteNext = false;
      this.executeUpdateTaskPromise = this.handleUpdate();
    }
  }

  protected getComplexTitleAfterGenAITitle(
    aiTitleOrCurTitle: string,
    isAITitle: boolean,
  ): {
    title: string;
    titleType: PatchThoughtDtoTitleTypeEnum;
  } {
    const curTitleType = this.thoughtComponent?.getTitleType();
    if (curTitleType === PatchThoughtDtoTitleTypeEnum.manual) {
      return {
        title: this.title || '',
        titleType: PatchThoughtDtoTitleTypeEnum.manual,
      };
    }
    if (isAITitle) {
      return {
        title: aiTitleOrCurTitle,
        titleType: PatchThoughtDtoTitleTypeEnum.ai,
      };
    }
    return {
      title: aiTitleOrCurTitle,
      titleType: curTitleType || PatchThoughtDtoTitleTypeEnum.default,
    };
  }

  getYDoc() {
    return this.ydoc;
  }

  editTitle(title: string) {
    // 过滤回车换行符
    const filteredTitle = title.replace(/[\r\n]/g, '');
    this.thoughtComponent?.setTitleType(PatchThoughtDtoTitleTypeEnum.manual);
    this.setTitle(filteredTitle);
    this.onEditorUpdate();
  }

  async manualUpdateAITitle() {
    const content = ThoughtContentUtils.getMarkdownData(this.editor.state.doc);
    if (content.length <= MIN_GEN_TITLE_CONTENT_LENGTH) {
      this.handleFailed('Content is too short to generate a meaningful title');
      return;
    }

    this.thoughtComponent?.setTitleType(PatchThoughtDtoTitleTypeEnum.manual);
    const oldTitle = this.title || '';
    this.setTitle('');
    this.thoughtComponent?.setIsGenTitle(true);

    const title = await this.thoughtTitleUtils.getAITitle({
      content,
      useCache: false,
    });

    this.thoughtComponent?.setIsGenTitle(false);
    const curTitle = this.title;

    // 之前已经将标题设置为空了，如果此时还有标题, 那么就不设置
    if (curTitle) {
      return;
    }

    // 确定生成了新标题时再更新
    if (title) {
      this.setTitle(title);
      this.boundOnEditorUpdate();
    } else {
      this.setTitle(oldTitle);
    }
  }

  setTitle(title: string) {
    this.thoughtComponent?.onUpdate?.({ title });
    this.thoughtTitleUtils.broadcastTitleChange(title);
    this.thoughtComponent?.setTitle(title);
  }

  handleSelectionChange = debounce(() => {
    const { selection } = this.editor.state;
    if (selection.empty) {
      this.thoughtComponent?.onSelectionChange?.({
        plainText: '',
        selectionType: SelectionType.CURSOR,
      });
      return;
    }

    const selectedContent = selection.content();
    const tempDoc = this.editor.state.doc.type.createAndFill();
    if (!tempDoc) return;

    const fragment = selectedContent.content;
    const nodeWithSelection = tempDoc.copy(fragment);
    const node = diffTransformUtils.extractContent(nodeWithSelection, DIFF_CHANGE_TYPE.ADDED);

    this.thoughtComponent?.onSelectionChange?.({
      plainText: getText(node),
      selectionType: SelectionType.SELECTION,
    });
  }, 100);

  get title() {
    return this.thoughtComponent?.getTitle();
  }

  get titleType() {
    return this.thoughtComponent?.getTitleType();
  }

  get thoughtComponent() {
    return this.componentRef.current;
  }

  async createManualVersion(title: string, description: string) {
    return this.versionUtils?.createManualVersion(title, description);
  }

  async getVersionList() {
    return this.versionUtils?.getVersionList();
  }

  async deleteVersion(versionId: string) {
    return this.versionUtils?.deleteVersion(versionId);
  }

  async restoreThought(version: ThoughtVersionDto) {
    return this.versionUtils?.restoreThought(version);
  }

  getId() {
    return this.id;
  }

  destroy() {
    this.editor.off('update', this.boundOnEditorUpdate);
    this.thoughtTitleUtils.destroy();
  }

  getThoughtData() {
    return {
      id: this.id,
      title: this.title,
      titleType: this.titleType,
      content: {
        raw: ThoughtContentUtils.getRawData(this.ydoc),
        plain: ThoughtContentUtils.getMarkdownData(this.editor.state.doc, DIFF_CHANGE_TYPE.ADDED),
      },
    };
  }

  getThoughtId() {
    return this.id;
  }
}
