import {
  IImageUpload<PERSON><PERSON>roller,
  Upload<PERSON>mageR<PERSON>,
  UploadImageStatus,
} from '@repo/ui-business-editor';
import { apiClient, callAPI, callHTTP } from '@/utils/callHTTP';
import { sha256File } from '@/utils/utils';

export class ImageUploadController implements IImageUploadController {
  private async calculateSha(file: File) {
    return await sha256File(file);
  }

  private async uploadImageCall(hash: string, file: File) {
    const { data: upload_url, error } = await callAPI(
      apiClient.fileApi.genSignedPutUrlIfNotExist({
        hash,
        is_public: false,
      }),
      {
        silent: true,
      },
    );
    // const { data: upload_url, error } = await callHTTP('/api/v1/genSignedPutUrlIfNotExist', {
    //   method: 'POST',
    //   body: { hash },
    // });
    if (error) {
      throw error;
    }
    if (upload_url?.url) {
      const headers: { 'Content-Type'?: string } = {};
      if (file?.type) {
        headers['Content-Type'] = file.type;
      }
      const { error: uploadError } = await callHTTP(upload_url.url, {
        method: 'PUT',
        body: file,
        headers,
      });
      if (uploadError) {
        throw uploadError;
      }
    }
  }

  async extractText(imageUrl: string) {
    let fullImageUrl = imageUrl;
    try {
      const url = new URL(imageUrl);
      fullImageUrl = url.toString();
    } catch {
      fullImageUrl = `${window.location.origin}${imageUrl}`;
    }

    try {
      const { data, error } = await callAPI(
        apiClient.textApi.extractText({
          url: fullImageUrl,
          adapt_image: true,
        }),
        {
          silent: true,
        },
      );
      // const { data, error } = await callHTTP('/api/v1/extractText', {
      //   method: 'POST',
      //   body: { url: fullImageUrl, adaptImage: true },
      //   silent: true,
      // });
      if (error) {
        return '';
      }
      // todo
      // return data.result.text;
      return '';
    } catch {
      return '';
    }
  }

  async uploadImage(file: File): Promise<UploadImageRes> {
    try {
      const hash = await this.calculateSha(file);
      await this.uploadImageCall(hash, file);
      return {
        status: UploadImageStatus.UPLOADED,
        url: `/files/${hash}`,
      };
    } catch (error) {
      return {
        status: UploadImageStatus.ERROR,
        url: '',
      };
    }
  }
}
