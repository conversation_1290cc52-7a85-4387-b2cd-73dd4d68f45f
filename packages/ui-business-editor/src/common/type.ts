import { LinkOptions, MermaidOptions } from '@repo/editor-common';
import { PlaceholderOptions } from '@tiptap/extension-placeholder';
import {
  BusinessImageExtensionOptions,
  BussinessDiffBlockOptions,
  CharacterCountOptions,
  KeyDownOptions,
  LocalCollaborationOptions,
} from '../extension';

export type EditorExtensionOptions = {
  keyDownOptions?: KeyDownOptions;
  characterCountOptions?: CharacterCountOptions;
  imageOptions?: Partial<BusinessImageExtensionOptions>;
  localCollaborationOptions?: LocalCollaborationOptions;
  linkOptions?: Partial<LinkOptions>;
  placeholderOptions?: Partial<PlaceholderOptions>;
  diffBlockOptions?: Partial<BussinessDiffBlockOptions>;
  mermaidOptions?: Partial<MermaidOptions>;
};
