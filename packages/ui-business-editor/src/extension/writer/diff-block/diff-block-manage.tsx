import { DiffBlockExtensionName, DiffBlockUpdateMetaKey } from '@repo/editor-common';
import type { Transaction } from '@tiptap/pm/state';
import type { Editor } from '@tiptap/react';
import React, { createRef } from 'react';
import { createRoot } from 'react-dom/client';
import {
  DiffBlockManageToolbar,
  type DiffBlockManageToolbarRef,
  type HoverDiffBlockParams,
  IBussinessDiffTransformUtils,
} from './diff-block-manage-toolbar';
import type { DiffBlockUpdateMeta } from './type';
import { getDiffBlockNodes } from './utils';
import './diff-block-manage.css';

const TOOLBAR_CONTAINER_ID = 'diff-block-manage-toolbar-container';

const UNDO_REDO_TR_MATE_KEY = 'y-sync$';

export class DiffBlockManage {
  private root: ReturnType<typeof createRoot> | null = null;
  private diffBlockManageToolbarRef: React.RefObject<DiffBlockManageToolbarRef> | null =
    createRef();

  constructor(
    private editor: Editor,
    private diffTransformUtils: IBussinessDiffTransformUtils,
  ) {}

  init() {
    this.editor.on('transaction', this.handleTransaction);
  }

  destroy() {
    this.editor.off('transaction', this.handleTransaction);
    this.hideManageToolbar();
  }

  checkHasDiffBlock() {
    const diffBlockType = this.editor.schema.nodes[DiffBlockExtensionName];
    if (!diffBlockType) {
      return false;
    }
    let hasDiffBlock = false;
    this.editor.state.doc.descendants((node) => {
      if (node.type === diffBlockType) {
        hasDiffBlock = true;
        return false;
      }
      return true;
    });
    return hasDiffBlock;
  }

  private getUndoRedoTrMate(transaction: Transaction) {
    return transaction.getMeta(UNDO_REDO_TR_MATE_KEY);
  }

  private handleTransaction = (props: { transaction: Transaction }) => {
    const { transaction } = props;

    const diffBlockUpdateMeta = this.getDiffBlockUpdateMeta(transaction);
    const undoRedoTrMate = this.getUndoRedoTrMate(transaction);
    if (!undoRedoTrMate && !diffBlockUpdateMeta) {
      return;
    }
    const hasDiffBlock = this.checkHasDiffBlock();
    // 没有 diff block 且没有 diff block update meta 则不显示管理工具栏
    if (!hasDiffBlock && !diffBlockUpdateMeta) {
      return;
    }
    if (this.diffBlockManageToolbarRef?.current) {
      this.diffBlockManageToolbarRef.current.updateDiffInfo(diffBlockUpdateMeta);
    } else {
      this.showManageToolbar();
    }
  };

  private getDiffBlockUpdateMeta(transaction: Transaction): DiffBlockUpdateMeta | undefined {
    return transaction.getMeta(DiffBlockUpdateMetaKey) as DiffBlockUpdateMeta | undefined;
  }

  showManageToolbar() {
    const diffBlockType = this.editor.schema.nodes[DiffBlockExtensionName];
    if (!diffBlockType) {
      console.error(`Node type "${DiffBlockExtensionName}" not found in schema.`);
      return;
    }

    let hasDiffBlock = false;
    this.editor.state.doc.descendants((node) => {
      if (node.type === diffBlockType) {
        hasDiffBlock = true;
        return false;
      }
      return true;
    });

    if (hasDiffBlock) {
      let container = this.editorContainer.ownerDocument.getElementById(TOOLBAR_CONTAINER_ID);
      if (!container) {
        container = this.editorContainer.ownerDocument.createElement('div');
        container.id = TOOLBAR_CONTAINER_ID;
        container.classList.add('diff-block-manage-toolbar-container');
        this.editorContainer.appendChild(container);
        this.root = createRoot(container);
      }
      if (this.root) {
        this.root.render(
          React.createElement(DiffBlockManageToolbar, {
            editor: this.editor,
            diffTransformUtils: this.diffTransformUtils,
            ref: this.diffBlockManageToolbarRef,
            diffBlockManage: this,
            initDiffBlockList: getDiffBlockNodes(this.editor),
          }),
        );
      }
    } else {
      this.hideManageToolbar();
    }
  }

  onHoverDiffBlock(params: HoverDiffBlockParams) {
    if (this.diffBlockManageToolbarRef?.current) {
      this.diffBlockManageToolbarRef.current.onHoverDiffBlock(params);
    }
  }

  hideManageToolbar() {
    const container = this.editorContainer.ownerDocument.getElementById(TOOLBAR_CONTAINER_ID);
    if (container) {
      this.root?.unmount();
      container.remove();
      this.root = null;
      this.diffBlockManageToolbarRef = createRef();
    }
  }

  reRenderDiffInfo() {
    if (this.diffBlockManageToolbarRef?.current) {
      this.diffBlockManageToolbarRef.current.reRenderDiffInfo();
    } else {
      this.showManageToolbar();
    }
  }

  get editorContainer() {
    if (!this.editor.view?.dom) {
      return document.body;
    }
    return this.editor.view.dom.parentElement || this.editor.view.dom;
  }
}

export type IDiffBlockManage = DiffBlockManage;

export const getDiffBlockManage = (editor: Editor): IDiffBlockManage | undefined => {
  const diffBlockManage = editor.storage.diffBlock.diffBlockManage;
  if (!diffBlockManage) {
    return undefined;
  }
  return diffBlockManage;
};
