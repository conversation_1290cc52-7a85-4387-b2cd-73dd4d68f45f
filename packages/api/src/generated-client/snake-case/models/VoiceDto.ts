/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { BoardItemDto } from './BoardItemDto';
import {
  BoardItemDtoFromJSON,
  BoardItemDtoFromJSONTyped,
  BoardItemDtoToJSON,
  BoardItemDtoToJSONTyped,
} from './BoardItemDto';
import type { ReaderHTMLContentDto } from './ReaderHTMLContentDto';
import {
  ReaderHTMLContentDtoFromJSON,
  ReaderHTMLContentDtoFromJSONTyped,
  ReaderHTMLContentDtoToJSON,
  ReaderHTMLContentDtoToJSONTyped,
} from './ReaderHTMLContentDto';
import type { VoiceDtoFile } from './VoiceDtoFile';
import {
  VoiceDtoFileFromJSON,
  VoiceDtoFileFromJSONTyped,
  VoiceDtoFileToJSON,
  VoiceDtoFileToJSONTyped,
} from './VoiceDtoFile';
import type { WebpageMetaDto } from './WebpageMetaDto';
import {
  WebpageMetaDtoFromJSON,
  WebpageMetaDtoFromJSONTyped,
  WebpageMetaDtoToJSON,
  WebpageMetaDtoToJSONTyped,
} from './WebpageMetaDto';

/**
 *
 * @export
 * @interface VoiceDto
 */
export interface VoiceDto {
  /**
   * 语音 ID
   * @type {string}
   * @memberof VoiceDto
   */
  id: string;
  /**
   * 创建时间
   * @type {Date}
   * @memberof VoiceDto
   */
  created_at: Date;
  /**
   * 更新时间
   * @type {Date}
   * @memberof VoiceDto
   */
  updated_at: Date;
  /**
   * 空间 ID
   * @type {string}
   * @memberof VoiceDto
   */
  space_id: string;
  /**
   * 创建者 ID
   * @type {string}
   * @memberof VoiceDto
   */
  creator_id: string;
  /**
   * 类型
   * @type {string}
   * @memberof VoiceDto
   */
  type: string;
  /**
   * 来源
   * @type {string}
   * @memberof VoiceDto
   */
  from: string;
  /**
   * 状态
   * @type {string}
   * @memberof VoiceDto
   */
  status?: string;
  /**
   * 语音标题
   * @type {string}
   * @memberof VoiceDto
   */
  title?: string;
  /**
   * 网页元信息
   * @type {WebpageMetaDto}
   * @memberof VoiceDto
   */
  webpage?: WebpageMetaDto;
  /**
   * 节目说明
   * @type {ReaderHTMLContentDto}
   * @memberof VoiceDto
   */
  show_notes?: ReaderHTMLContentDto;
  /**
   * 语音作者列表
   * @type {Array<object>}
   * @memberof VoiceDto
   */
  authors?: Array<object>;
  /**
   * 封面图片 URL
   * @type {string}
   * @memberof VoiceDto
   */
  hero_image_url?: string;
  /**
   * 发布时间
   * @type {Date}
   * @memberof VoiceDto
   */
  published_at?: Date;
  /**
   * 播放 URL
   * @type {string}
   * @memberof VoiceDto
   */
  play_url?: string;
  /**
   *
   * @type {VoiceDtoFile}
   * @memberof VoiceDto
   */
  file?: VoiceDtoFile;
  /**
   * 可见性
   * @type {string}
   * @memberof VoiceDto
   */
  visibility?: string;
  /**
   * 创作板项目信息
   * @type {BoardItemDto}
   * @memberof VoiceDto
   */
  board_item?: BoardItemDto;
  /**
   * 额外数据
   * @type {object}
   * @memberof VoiceDto
   */
  extra?: object;
  /**
   * 板块ID列表
   * @type {Array<string>}
   * @memberof VoiceDto
   */
  board_ids?: Array<string>;
}

/**
 * Check if a given object implements the VoiceDto interface.
 */
export function instanceOfVoiceDto(value: object): value is VoiceDto {
  if (!('id' in value) || value.id === undefined) return false;
  if (!('created_at' in value) || value.created_at === undefined) return false;
  if (!('updated_at' in value) || value.updated_at === undefined) return false;
  if (!('space_id' in value) || value.space_id === undefined) return false;
  if (!('creator_id' in value) || value.creator_id === undefined) return false;
  if (!('type' in value) || value.type === undefined) return false;
  if (!('from' in value) || value.from === undefined) return false;
  return true;
}

export function VoiceDtoFromJSON(json: any): VoiceDto {
  return VoiceDtoFromJSONTyped(json, false);
}

export function VoiceDtoFromJSONTyped(json: any, _ignoreDiscriminator: boolean): VoiceDto {
  if (json == null) {
    return json;
  }
  return {
    id: json.id,
    created_at: new Date(json.created_at),
    updated_at: new Date(json.updated_at),
    space_id: json.space_id,
    creator_id: json.creator_id,
    type: json.type,
    from: json.from,
    status: json.status == null ? undefined : json.status,
    title: json.title == null ? undefined : json.title,
    webpage: json.webpage == null ? undefined : WebpageMetaDtoFromJSON(json.webpage),
    show_notes: json.show_notes == null ? undefined : ReaderHTMLContentDtoFromJSON(json.show_notes),
    authors: json.authors == null ? undefined : json.authors,
    hero_image_url: json.hero_image_url == null ? undefined : json.hero_image_url,
    published_at: json.published_at == null ? undefined : new Date(json.published_at),
    play_url: json.play_url == null ? undefined : json.play_url,
    file: json.file == null ? undefined : VoiceDtoFileFromJSON(json.file),
    visibility: json.visibility == null ? undefined : json.visibility,
    board_item: json.board_item == null ? undefined : BoardItemDtoFromJSON(json.board_item),
    extra: json.extra == null ? undefined : json.extra,
    board_ids: json.board_ids == null ? undefined : json.board_ids,
  };
}

export function VoiceDtoToJSON(json: any): VoiceDto {
  return VoiceDtoToJSONTyped(json, false);
}

export function VoiceDtoToJSONTyped(
  value?: VoiceDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    id: value.id,
    created_at: value.created_at.toISOString(),
    updated_at: value.updated_at.toISOString(),
    space_id: value.space_id,
    creator_id: value.creator_id,
    type: value.type,
    from: value.from,
    status: value.status,
    title: value.title,
    webpage: WebpageMetaDtoToJSON(value.webpage),
    show_notes: ReaderHTMLContentDtoToJSON(value.show_notes),
    authors: value.authors,
    hero_image_url: value.hero_image_url,
    published_at: value.published_at == null ? undefined : value.published_at.toISOString(),
    play_url: value.play_url,
    file: VoiceDtoFileToJSON(value.file),
    visibility: value.visibility,
    board_item: BoardItemDtoToJSON(value.board_item),
    extra: value.extra,
    board_ids: value.board_ids,
  };
}
