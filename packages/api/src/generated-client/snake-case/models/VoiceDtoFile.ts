/* tslint:disable */
/* eslint-disable */
/**
 * You<PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
/**
 * 文件信息
 * @export
 * @interface VoiceDtoFile
 */
export interface VoiceDtoFile {
  /**
   * 文件名
   * @type {string}
   * @memberof VoiceDtoFile
   */
  name?: string;
  /**
   * MIME类型
   * @type {string}
   * @memberof VoiceDtoFile
   */
  mime_type?: string;
  /**
   * 文件大小
   * @type {number}
   * @memberof VoiceDtoFile
   */
  size?: number;
  /**
   * 存储URL
   * @type {string}
   * @memberof VoiceDtoFile
   */
  storage_url?: string;
  /**
   * CDN访问URL
   * @type {string}
   * @memberof VoiceDtoFile
   */
  url?: string;
  /**
   * 原始URL
   * @type {string}
   * @memberof VoiceDtoFile
   */
  original_url?: string;
}

/**
 * Check if a given object implements the VoiceDtoFile interface.
 */
export function instanceOfVoiceDtoFile(value: object): value is VoiceDtoFile {
  return true;
}

export function VoiceDtoFileFromJSON(json: any): VoiceDtoFile {
  return VoiceDtoFileFromJSONTyped(json, false);
}

export function VoiceDtoFileFromJSONTyped(json: any, _ignoreDiscriminator: boolean): VoiceDtoFile {
  if (json == null) {
    return json;
  }
  return {
    name: json.name == null ? undefined : json.name,
    mime_type: json.mime_type == null ? undefined : json.mime_type,
    size: json.size == null ? undefined : json.size,
    storage_url: json.storage_url == null ? undefined : json.storage_url,
    url: json.url == null ? undefined : json.url,
    original_url: json.original_url == null ? undefined : json.original_url,
  };
}

export function VoiceDtoFileToJSON(json: any): VoiceDtoFile {
  return VoiceDtoFileToJSONTyped(json, false);
}

export function VoiceDtoFileToJSONTyped(
  value?: VoiceDtoFile | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    name: value.name,
    mime_type: value.mime_type,
    size: value.size,
    storage_url: value.storage_url,
    url: value.url,
    original_url: value.original_url,
  };
}
