/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { BoardItemDto } from './BoardItemDto';
import {
  BoardItemDtoFromJSON,
  BoardItemDtoFromJSONTyped,
  BoardItemDtoToJSON,
  BoardItemDtoToJSONTyped,
} from './BoardItemDto';
import type { ReaderHTMLContentDto } from './ReaderHTMLContentDto';
import {
  ReaderHTMLContentDtoFromJSON,
  ReaderHTMLContentDtoFromJSONTyped,
  ReaderHTMLContentDtoToJSON,
  ReaderHTMLContentDtoToJSONTyped,
} from './ReaderHTMLContentDto';
import type { VoiceDtoFile } from './VoiceDtoFile';
import {
  VoiceDtoFileFromJSON,
  VoiceDtoFileFromJSONTyped,
  VoiceDtoFileToJSON,
  VoiceDtoFileToJSONTyped,
} from './VoiceDtoFile';
import type { WebpageMetaDto } from './WebpageMetaDto';
import {
  WebpageMetaDtoFromJSON,
  WebpageMetaDtoFromJSONTyped,
  WebpageMetaDtoToJSON,
  WebpageMetaDtoToJSONTyped,
} from './WebpageMetaDto';

/**
 *
 * @export
 * @interface PdfDto
 */
export interface PdfDto {
  /**
   * PDF文档 ID
   * @type {string}
   * @memberof PdfDto
   */
  id: string;
  /**
   * 创建时间
   * @type {Date}
   * @memberof PdfDto
   */
  created_at: Date;
  /**
   * 更新时间
   * @type {Date}
   * @memberof PdfDto
   */
  updated_at: Date;
  /**
   * 空间 ID
   * @type {string}
   * @memberof PdfDto
   */
  space_id: string;
  /**
   * 创建者 ID
   * @type {string}
   * @memberof PdfDto
   */
  creator_id: string;
  /**
   * PDF文档标题
   * @type {string}
   * @memberof PdfDto
   */
  title?: string;
  /**
   * 类型
   * @type {string}
   * @memberof PdfDto
   */
  type?: string;
  /**
   * 来源
   * @type {string}
   * @memberof PdfDto
   */
  from?: string;
  /**
   * 状态
   * @type {string}
   * @memberof PdfDto
   */
  status?: string;
  /**
   * 父级ID
   * @type {string}
   * @memberof PdfDto
   */
  parent_id?: string;
  /**
   * 板块ID列表
   * @type {Array<string>}
   * @memberof PdfDto
   */
  board_ids?: Array<string>;
  /**
   * 解析后的PDF内容
   * @type {ReaderHTMLContentDto}
   * @memberof PdfDto
   */
  content?: ReaderHTMLContentDto;
  /**
   * 网页元信息（URL创建时）
   * @type {WebpageMetaDto}
   * @memberof PdfDto
   */
  webpage?: WebpageMetaDto;
  /**
   * PDF作者列表
   * @type {Array<object>}
   * @memberof PdfDto
   */
  authors?: Array<object>;
  /**
   * 封面图片 URL
   * @type {string}
   * @memberof PdfDto
   */
  hero_image_url?: string;
  /**
   * 发布时间
   * @type {Date}
   * @memberof PdfDto
   */
  published_at?: Date;
  /**
   * 内容格式
   * @type {string}
   * @memberof PdfDto
   */
  content_format?: string;
  /**
   * 原始内容
   * @type {string}
   * @memberof PdfDto
   */
  content_raw?: string;
  /**
   * 纯文本内容
   * @type {string}
   * @memberof PdfDto
   */
  content_plain?: string;
  /**
   * 内容语言
   * @type {string}
   * @memberof PdfDto
   */
  content_language?: string;
  /**
   * 可见性
   * @type {string}
   * @memberof PdfDto
   */
  visibility?: string;
  /**
   * 创作板项目信息
   * @type {BoardItemDto}
   * @memberof PdfDto
   */
  board_item?: BoardItemDto;
  /**
   * 额外数据
   * @type {object}
   * @memberof PdfDto
   */
  extra?: object;
  /**
   *
   * @type {VoiceDtoFile}
   * @memberof PdfDto
   */
  file?: VoiceDtoFile;
}

/**
 * Check if a given object implements the PdfDto interface.
 */
export function instanceOfPdfDto(value: object): value is PdfDto {
  if (!('id' in value) || value.id === undefined) return false;
  if (!('created_at' in value) || value.created_at === undefined) return false;
  if (!('updated_at' in value) || value.updated_at === undefined) return false;
  if (!('space_id' in value) || value.space_id === undefined) return false;
  if (!('creator_id' in value) || value.creator_id === undefined) return false;
  return true;
}

export function PdfDtoFromJSON(json: any): PdfDto {
  return PdfDtoFromJSONTyped(json, false);
}

export function PdfDtoFromJSONTyped(json: any, _ignoreDiscriminator: boolean): PdfDto {
  if (json == null) {
    return json;
  }
  return {
    id: json.id,
    created_at: new Date(json.created_at),
    updated_at: new Date(json.updated_at),
    space_id: json.space_id,
    creator_id: json.creator_id,
    title: json.title == null ? undefined : json.title,
    type: json.type == null ? undefined : json.type,
    from: json.from == null ? undefined : json.from,
    status: json.status == null ? undefined : json.status,
    parent_id: json.parent_id == null ? undefined : json.parent_id,
    board_ids: json.board_ids == null ? undefined : json.board_ids,
    content: json.content == null ? undefined : ReaderHTMLContentDtoFromJSON(json.content),
    webpage: json.webpage == null ? undefined : WebpageMetaDtoFromJSON(json.webpage),
    authors: json.authors == null ? undefined : json.authors,
    hero_image_url: json.hero_image_url == null ? undefined : json.hero_image_url,
    published_at: json.published_at == null ? undefined : new Date(json.published_at),
    content_format: json.content_format == null ? undefined : json.content_format,
    content_raw: json.content_raw == null ? undefined : json.content_raw,
    content_plain: json.content_plain == null ? undefined : json.content_plain,
    content_language: json.content_language == null ? undefined : json.content_language,
    visibility: json.visibility == null ? undefined : json.visibility,
    board_item: json.board_item == null ? undefined : BoardItemDtoFromJSON(json.board_item),
    extra: json.extra == null ? undefined : json.extra,
    file: json.file == null ? undefined : VoiceDtoFileFromJSON(json.file),
  };
}

export function PdfDtoToJSON(json: any): PdfDto {
  return PdfDtoToJSONTyped(json, false);
}

export function PdfDtoToJSONTyped(
  value?: PdfDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    id: value.id,
    created_at: value.created_at.toISOString(),
    updated_at: value.updated_at.toISOString(),
    space_id: value.space_id,
    creator_id: value.creator_id,
    title: value.title,
    type: value.type,
    from: value.from,
    status: value.status,
    parent_id: value.parent_id,
    board_ids: value.board_ids,
    content: ReaderHTMLContentDtoToJSON(value.content),
    webpage: WebpageMetaDtoToJSON(value.webpage),
    authors: value.authors,
    hero_image_url: value.hero_image_url,
    published_at: value.published_at == null ? undefined : value.published_at.toISOString(),
    content_format: value.content_format,
    content_raw: value.content_raw,
    content_plain: value.content_plain,
    content_language: value.content_language,
    visibility: value.visibility,
    board_item: BoardItemDtoToJSON(value.board_item),
    extra: value.extra,
    file: VoiceDtoFileToJSON(value.file),
  };
}
