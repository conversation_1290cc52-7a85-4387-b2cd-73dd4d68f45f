/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { BoardItemDto } from './BoardItemDto';
import {
  BoardItemDtoFromJSON,
  BoardItemDtoFromJSONTyped,
  BoardItemDtoToJSON,
  BoardItemDtoToJSONTyped,
} from './BoardItemDto';
import type { ReaderHTMLContentDto } from './ReaderHTMLContentDto';
import {
  ReaderHTMLContentDtoFromJSON,
  ReaderHTMLContentDtoFromJSONTyped,
  ReaderHTMLContentDtoToJSON,
  ReaderHTMLContentDtoToJSONTyped,
} from './ReaderHTMLContentDto';
import type { VoiceDtoFile } from './VoiceDtoFile';
import {
  VoiceDtoFileFromJSON,
  VoiceDtoFileFromJSONTyped,
  VoiceDtoFileToJSON,
  VoiceDtoFileToJSONTyped,
} from './VoiceDtoFile';
import type { WebpageMetaDto } from './WebpageMetaDto';
import {
  WebpageMetaDtoFromJSON,
  WebpageMetaDtoFromJSONTyped,
  WebpageMetaDtoToJSON,
  WebpageMetaDtoToJSONTyped,
} from './WebpageMetaDto';

/**
 *
 * @export
 * @interface PdfDto
 */
export interface PdfDto {
  /**
   * PDF文档 ID
   * @type {string}
   * @memberof PdfDto
   */
  id: string;
  /**
   * 创建时间
   * @type {Date}
   * @memberof PdfDto
   */
  createdAt: Date;
  /**
   * 更新时间
   * @type {Date}
   * @memberof PdfDto
   */
  updatedAt: Date;
  /**
   * 空间 ID
   * @type {string}
   * @memberof PdfDto
   */
  spaceId: string;
  /**
   * 创建者 ID
   * @type {string}
   * @memberof PdfDto
   */
  creatorId: string;
  /**
   * PDF文档标题
   * @type {string}
   * @memberof PdfDto
   */
  title?: string;
  /**
   * 类型
   * @type {string}
   * @memberof PdfDto
   */
  type?: string;
  /**
   * 来源
   * @type {string}
   * @memberof PdfDto
   */
  from?: string;
  /**
   * 状态
   * @type {string}
   * @memberof PdfDto
   */
  status?: string;
  /**
   * 父级ID
   * @type {string}
   * @memberof PdfDto
   */
  parentId?: string;
  /**
   * 板块ID列表
   * @type {Array<string>}
   * @memberof PdfDto
   */
  boardIds?: Array<string>;
  /**
   * 解析后的PDF内容
   * @type {ReaderHTMLContentDto}
   * @memberof PdfDto
   */
  content?: ReaderHTMLContentDto;
  /**
   * 网页元信息（URL创建时）
   * @type {WebpageMetaDto}
   * @memberof PdfDto
   */
  webpage?: WebpageMetaDto;
  /**
   * PDF作者列表
   * @type {Array<object>}
   * @memberof PdfDto
   */
  authors?: Array<object>;
  /**
   * 封面图片 URL
   * @type {string}
   * @memberof PdfDto
   */
  heroImageUrl?: string;
  /**
   * 发布时间
   * @type {Date}
   * @memberof PdfDto
   */
  publishedAt?: Date;
  /**
   * 内容格式
   * @type {string}
   * @memberof PdfDto
   */
  contentFormat?: string;
  /**
   * 原始内容
   * @type {string}
   * @memberof PdfDto
   */
  contentRaw?: string;
  /**
   * 纯文本内容
   * @type {string}
   * @memberof PdfDto
   */
  contentPlain?: string;
  /**
   * 内容语言
   * @type {string}
   * @memberof PdfDto
   */
  contentLanguage?: string;
  /**
   * 可见性
   * @type {string}
   * @memberof PdfDto
   */
  visibility?: string;
  /**
   * 创作板项目信息
   * @type {BoardItemDto}
   * @memberof PdfDto
   */
  boardItem?: BoardItemDto;
  /**
   * 额外数据
   * @type {object}
   * @memberof PdfDto
   */
  extra?: object;
  /**
   *
   * @type {VoiceDtoFile}
   * @memberof PdfDto
   */
  file?: VoiceDtoFile;
}

/**
 * Check if a given object implements the PdfDto interface.
 */
export function instanceOfPdfDto(value: object): value is PdfDto {
  if (!('id' in value) || value.id === undefined) return false;
  if (!('createdAt' in value) || value.createdAt === undefined) return false;
  if (!('updatedAt' in value) || value.updatedAt === undefined) return false;
  if (!('spaceId' in value) || value.spaceId === undefined) return false;
  if (!('creatorId' in value) || value.creatorId === undefined) return false;
  return true;
}

export function PdfDtoFromJSON(json: any): PdfDto {
  return PdfDtoFromJSONTyped(json, false);
}

export function PdfDtoFromJSONTyped(json: any, _ignoreDiscriminator: boolean): PdfDto {
  if (json == null) {
    return json;
  }
  return {
    id: json.id,
    createdAt: new Date(json.createdAt),
    updatedAt: new Date(json.updatedAt),
    spaceId: json.spaceId,
    creatorId: json.creatorId,
    title: json.title == null ? undefined : json.title,
    type: json.type == null ? undefined : json.type,
    from: json.from == null ? undefined : json.from,
    status: json.status == null ? undefined : json.status,
    parentId: json.parentId == null ? undefined : json.parentId,
    boardIds: json.boardIds == null ? undefined : json.boardIds,
    content: json.content == null ? undefined : ReaderHTMLContentDtoFromJSON(json.content),
    webpage: json.webpage == null ? undefined : WebpageMetaDtoFromJSON(json.webpage),
    authors: json.authors == null ? undefined : json.authors,
    heroImageUrl: json.heroImageUrl == null ? undefined : json.heroImageUrl,
    publishedAt: json.publishedAt == null ? undefined : new Date(json.publishedAt),
    contentFormat: json.contentFormat == null ? undefined : json.contentFormat,
    contentRaw: json.contentRaw == null ? undefined : json.contentRaw,
    contentPlain: json.contentPlain == null ? undefined : json.contentPlain,
    contentLanguage: json.contentLanguage == null ? undefined : json.contentLanguage,
    visibility: json.visibility == null ? undefined : json.visibility,
    boardItem: json.boardItem == null ? undefined : BoardItemDtoFromJSON(json.boardItem),
    extra: json.extra == null ? undefined : json.extra,
    file: json.file == null ? undefined : VoiceDtoFileFromJSON(json.file),
  };
}

export function PdfDtoToJSON(json: any): PdfDto {
  return PdfDtoToJSONTyped(json, false);
}

export function PdfDtoToJSONTyped(
  value?: PdfDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    id: value.id,
    createdAt: value.createdAt.toISOString(),
    updatedAt: value.updatedAt.toISOString(),
    spaceId: value.spaceId,
    creatorId: value.creatorId,
    title: value.title,
    type: value.type,
    from: value.from,
    status: value.status,
    parentId: value.parentId,
    boardIds: value.boardIds,
    content: ReaderHTMLContentDtoToJSON(value.content),
    webpage: WebpageMetaDtoToJSON(value.webpage),
    authors: value.authors,
    heroImageUrl: value.heroImageUrl,
    publishedAt: value.publishedAt == null ? undefined : value.publishedAt.toISOString(),
    contentFormat: value.contentFormat,
    contentRaw: value.contentRaw,
    contentPlain: value.contentPlain,
    contentLanguage: value.contentLanguage,
    visibility: value.visibility,
    boardItem: BoardItemDtoToJSON(value.boardItem),
    extra: value.extra,
    file: VoiceDtoFileToJSON(value.file),
  };
}
