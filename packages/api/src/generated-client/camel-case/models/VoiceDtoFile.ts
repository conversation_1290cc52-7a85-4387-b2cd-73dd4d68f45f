/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
/**
 * 文件信息
 * @export
 * @interface VoiceDtoFile
 */
export interface VoiceDtoFile {
  /**
   * 文件名
   * @type {string}
   * @memberof VoiceDtoFile
   */
  name?: string;
  /**
   * MIME类型
   * @type {string}
   * @memberof VoiceDtoFile
   */
  mimeType?: string;
  /**
   * 文件大小
   * @type {number}
   * @memberof VoiceDtoFile
   */
  size?: number;
  /**
   * 存储URL
   * @type {string}
   * @memberof VoiceDtoFile
   */
  storageUrl?: string;
  /**
   * CDN访问URL
   * @type {string}
   * @memberof VoiceDtoFile
   */
  url?: string;
  /**
   * 原始URL
   * @type {string}
   * @memberof VoiceDtoFile
   */
  originalUrl?: string;
}

/**
 * Check if a given object implements the VoiceDtoFile interface.
 */
export function instanceOfVoiceDtoFile(value: object): value is VoiceDtoFile {
  return true;
}

export function VoiceDtoFileFromJSON(json: any): VoiceDtoFile {
  return VoiceDtoFileFromJSONTyped(json, false);
}

export function VoiceDtoFileFromJSONTyped(json: any, _ignoreDiscriminator: boolean): VoiceDtoFile {
  if (json == null) {
    return json;
  }
  return {
    name: json.name == null ? undefined : json.name,
    mimeType: json.mimeType == null ? undefined : json.mimeType,
    size: json.size == null ? undefined : json.size,
    storageUrl: json.storageUrl == null ? undefined : json.storageUrl,
    url: json.url == null ? undefined : json.url,
    originalUrl: json.originalUrl == null ? undefined : json.originalUrl,
  };
}

export function VoiceDtoFileToJSON(json: any): VoiceDtoFile {
  return VoiceDtoFileToJSONTyped(json, false);
}

export function VoiceDtoFileToJSONTyped(
  value?: VoiceDtoFile | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    name: value.name,
    mimeType: value.mimeType,
    size: value.size,
    storageUrl: value.storageUrl,
    url: value.url,
    originalUrl: value.originalUrl,
  };
}
