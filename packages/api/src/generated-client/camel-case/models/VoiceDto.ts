/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { BoardItemDto } from './BoardItemDto';
import {
  BoardItemDtoFromJSON,
  BoardItemDtoFromJSONTyped,
  BoardItemDtoToJSON,
  BoardItemDtoToJSONTyped,
} from './BoardItemDto';
import type { ReaderHTMLContentDto } from './ReaderHTMLContentDto';
import {
  ReaderHTMLContentDtoFromJSON,
  ReaderHTMLContentDtoFromJSONTyped,
  ReaderHTMLContentDtoToJSON,
  ReaderHTMLContentDtoToJSONTyped,
} from './ReaderHTMLContentDto';
import type { VoiceDtoFile } from './VoiceDtoFile';
import {
  VoiceDtoFileFromJSON,
  VoiceDtoFileFromJSONTyped,
  VoiceDtoFileToJSON,
  VoiceDtoFileToJSONTyped,
} from './VoiceDtoFile';
import type { WebpageMetaDto } from './WebpageMetaDto';
import {
  WebpageMetaDtoFromJSON,
  WebpageMetaDtoFromJSONTyped,
  WebpageMetaDtoToJSON,
  WebpageMetaDtoToJSONTyped,
} from './WebpageMetaDto';

/**
 *
 * @export
 * @interface VoiceDto
 */
export interface VoiceDto {
  /**
   * 语音 ID
   * @type {string}
   * @memberof VoiceDto
   */
  id: string;
  /**
   * 创建时间
   * @type {Date}
   * @memberof VoiceDto
   */
  createdAt: Date;
  /**
   * 更新时间
   * @type {Date}
   * @memberof VoiceDto
   */
  updatedAt: Date;
  /**
   * 空间 ID
   * @type {string}
   * @memberof VoiceDto
   */
  spaceId: string;
  /**
   * 创建者 ID
   * @type {string}
   * @memberof VoiceDto
   */
  creatorId: string;
  /**
   * 类型
   * @type {string}
   * @memberof VoiceDto
   */
  type: string;
  /**
   * 来源
   * @type {string}
   * @memberof VoiceDto
   */
  from: string;
  /**
   * 状态
   * @type {string}
   * @memberof VoiceDto
   */
  status?: string;
  /**
   * 语音标题
   * @type {string}
   * @memberof VoiceDto
   */
  title?: string;
  /**
   * 网页元信息
   * @type {WebpageMetaDto}
   * @memberof VoiceDto
   */
  webpage?: WebpageMetaDto;
  /**
   * 节目说明
   * @type {ReaderHTMLContentDto}
   * @memberof VoiceDto
   */
  showNotes?: ReaderHTMLContentDto;
  /**
   * 语音作者列表
   * @type {Array<object>}
   * @memberof VoiceDto
   */
  authors?: Array<object>;
  /**
   * 封面图片 URL
   * @type {string}
   * @memberof VoiceDto
   */
  heroImageUrl?: string;
  /**
   * 发布时间
   * @type {Date}
   * @memberof VoiceDto
   */
  publishedAt?: Date;
  /**
   * 播放 URL
   * @type {string}
   * @memberof VoiceDto
   */
  playUrl?: string;
  /**
   *
   * @type {VoiceDtoFile}
   * @memberof VoiceDto
   */
  file?: VoiceDtoFile;
  /**
   * 可见性
   * @type {string}
   * @memberof VoiceDto
   */
  visibility?: string;
  /**
   * 创作板项目信息
   * @type {BoardItemDto}
   * @memberof VoiceDto
   */
  boardItem?: BoardItemDto;
  /**
   * 额外数据
   * @type {object}
   * @memberof VoiceDto
   */
  extra?: object;
  /**
   * 板块ID列表
   * @type {Array<string>}
   * @memberof VoiceDto
   */
  boardIds?: Array<string>;
}

/**
 * Check if a given object implements the VoiceDto interface.
 */
export function instanceOfVoiceDto(value: object): value is VoiceDto {
  if (!('id' in value) || value.id === undefined) return false;
  if (!('createdAt' in value) || value.createdAt === undefined) return false;
  if (!('updatedAt' in value) || value.updatedAt === undefined) return false;
  if (!('spaceId' in value) || value.spaceId === undefined) return false;
  if (!('creatorId' in value) || value.creatorId === undefined) return false;
  if (!('type' in value) || value.type === undefined) return false;
  if (!('from' in value) || value.from === undefined) return false;
  return true;
}

export function VoiceDtoFromJSON(json: any): VoiceDto {
  return VoiceDtoFromJSONTyped(json, false);
}

export function VoiceDtoFromJSONTyped(json: any, _ignoreDiscriminator: boolean): VoiceDto {
  if (json == null) {
    return json;
  }
  return {
    id: json.id,
    createdAt: new Date(json.createdAt),
    updatedAt: new Date(json.updatedAt),
    spaceId: json.spaceId,
    creatorId: json.creatorId,
    type: json.type,
    from: json.from,
    status: json.status == null ? undefined : json.status,
    title: json.title == null ? undefined : json.title,
    webpage: json.webpage == null ? undefined : WebpageMetaDtoFromJSON(json.webpage),
    showNotes: json.showNotes == null ? undefined : ReaderHTMLContentDtoFromJSON(json.showNotes),
    authors: json.authors == null ? undefined : json.authors,
    heroImageUrl: json.heroImageUrl == null ? undefined : json.heroImageUrl,
    publishedAt: json.publishedAt == null ? undefined : new Date(json.publishedAt),
    playUrl: json.playUrl == null ? undefined : json.playUrl,
    file: json.file == null ? undefined : VoiceDtoFileFromJSON(json.file),
    visibility: json.visibility == null ? undefined : json.visibility,
    boardItem: json.boardItem == null ? undefined : BoardItemDtoFromJSON(json.boardItem),
    extra: json.extra == null ? undefined : json.extra,
    boardIds: json.boardIds == null ? undefined : json.boardIds,
  };
}

export function VoiceDtoToJSON(json: any): VoiceDto {
  return VoiceDtoToJSONTyped(json, false);
}

export function VoiceDtoToJSONTyped(
  value?: VoiceDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    id: value.id,
    createdAt: value.createdAt.toISOString(),
    updatedAt: value.updatedAt.toISOString(),
    spaceId: value.spaceId,
    creatorId: value.creatorId,
    type: value.type,
    from: value.from,
    status: value.status,
    title: value.title,
    webpage: WebpageMetaDtoToJSON(value.webpage),
    showNotes: ReaderHTMLContentDtoToJSON(value.showNotes),
    authors: value.authors,
    heroImageUrl: value.heroImageUrl,
    publishedAt: value.publishedAt == null ? undefined : value.publishedAt.toISOString(),
    playUrl: value.playUrl,
    file: VoiceDtoFileToJSON(value.file),
    visibility: value.visibility,
    boardItem: BoardItemDtoToJSON(value.boardItem),
    extra: value.extra,
    boardIds: value.boardIds,
  };
}
