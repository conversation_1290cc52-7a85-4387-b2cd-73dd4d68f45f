import { Fragment, type Node } from '@tiptap/pm/model';
import {
  DIFF_ACTION,
  DIFF_CHANGE_ATTR_NAME,
  DIFF_CHANGE_NAME,
  DIFF_CHANGE_TYPE,
  DiffBlockExtensionName,
  ViewMode,
} from '../const';
import { Mermaid } from '../extensions/mermaid';
import { convertBase64ToProseMirrorNode } from '../utils';

interface ExtractContentParams {
  base64Content: string;
  extractType: DIFF_CHANGE_TYPE;
}

export interface ReportWriterDataParams {
  thought_id: string;
  action: DIFF_ACTION;
  nodes: Array<{
    id: string;
    new: string;
    old: string;
  }>;
  resolve_version?: {
    thought_title: string;
    content: {
      raw: string;
      plain: string;
    };
  };
}

// 这个类是用来从 diff 态的内容根据要求提取出对应的正常内容
export class DiffTransformUtils {
  extractContentFromBase64(params: ExtractContentParams) {
    const { base64Content, extractType } = params;
    const prosemirrorDocNode = convertBase64ToProseMirrorNode(base64Content);

    // 提取对应的内容
    return this.extractContent(prosemirrorDocNode, extractType);
  }

  /**
   * 从 Diff Block 中根据操作类型提取干净的内容（无 diff 标记和 diff blocks）
   * @param node Diff Block 节点
   * @param action 操作类型（接受或拒绝）
   * @returns 处理后的内容
   */
  cleanDiffBlock(node: Node, action: DIFF_ACTION): Fragment {
    // 根据操作类型确定提取类型
    const extractType =
      action === DIFF_ACTION.ACCEPT ? DIFF_CHANGE_TYPE.ADDED : DIFF_CHANGE_TYPE.REMOVED;

    // 提取对应的内容
    const processedContent = this.processNodeForExtraction(node, extractType);

    // 处理差异块
    return this.processDiffBlocks(processedContent);
  }

  /**
   * 从节点中根据指定类型提取内容
   * @param doc 文档节点
   * @param extractType 提取类型
   * @returns 提取后的文档节点
   */
  extractContent(doc: Node, extractType: DIFF_CHANGE_TYPE): Node {
    // 首先处理常规的差异标记和属性
    let processedContent = this.processNodeForExtraction(doc, extractType);

    // 如果提供了差异块扩展名称，处理差异块
    processedContent = this.processDiffBlocks(processedContent);

    // 创建一个新的文档节点，使用相同的类型和属性，但使用新的内容
    return doc.type.create(doc.attrs, processedContent);
  }

  /**
   * 处理差异块，将其替换为其内容
   * @param fragment 要处理的片段
   * @returns 处理后的片段
   */
  private processDiffBlocks(fragment: Fragment): Fragment {
    const nodes: Node[] = [];

    // 遍历所有子节点
    fragment.forEach((node) => {
      if (node.type.name === DiffBlockExtensionName) {
        // 对于差异块，直接处理其内容，不再判断是否为空
        // 因为在 diff 上下文中，即使是空段落也可能是有意义的（比如恢复被删除的空段落）
        const processedContent = this.processDiffBlocks(node.content);
        // 将处理后的内容添加到结果中（展开差异块）
        processedContent.forEach((child) => nodes.push(child));
      } else if (node.content.size > 0) {
        // 对于非差异块节点，递归处理其内容
        const processedContent = this.processDiffBlocks(node.content);
        // 创建一个新节点，但去除差异属性
        const newAttrs = { ...node.attrs };
        delete newAttrs[DIFF_CHANGE_ATTR_NAME];

        // 如果是 mermaid 节点，确保 mode 为 edit
        if (node.type.name === Mermaid.name) {
          newAttrs.mode = ViewMode.Edit;
        }

        nodes.push(node.type.create(newAttrs, processedContent));
      } else {
        // 叶子节点，清理属性后添加
        if (node.attrs && node.attrs[DIFF_CHANGE_ATTR_NAME] !== undefined) {
          const newAttrs = { ...node.attrs };
          delete newAttrs[DIFF_CHANGE_ATTR_NAME];

          // 如果是 mermaid 节点，确保 mode 为 edit
          if (node.type.name === Mermaid.name) {
            newAttrs.mode = ViewMode.Edit;
          }

          nodes.push(node.type.create(newAttrs, node.content));
        } else {
          // 没有差异属性的节点，如果是 mermaid 节点也要调整 mode
          if (node.type.name === Mermaid.name && node.attrs.mode !== ViewMode.Edit) {
            const newAttrs = { ...node.attrs, mode: ViewMode.Edit };
            nodes.push(node.type.create(newAttrs, node.content));
          } else {
            // 没有差异属性的节点直接添加
            nodes.push(node);
          }
        }
      }
    });

    return Fragment.from(nodes);
  }

  /**
   * 处理节点及其子节点，根据提取类型决定是否保留
   * @param node 当前节点
   * @param extractType 提取类型
   * @returns 处理后的节点片段
   */
  private processNodeForExtraction(node: Node, extractType: DIFF_CHANGE_TYPE): Fragment {
    if (node.isLeaf) {
      // 对于叶子节点，直接根据标记决定是保留还是过滤
      if (node.isText) {
        return this.processTextNodeForExtraction(node, extractType);
      } else {
        return this.processSingleNodeForExtraction(node, extractType)
          ? Fragment.from(node)
          : Fragment.empty;
      }
    }

    // 处理非叶子节点
    const newChildren: Node[] = [];

    node.forEach((child) => {
      // 检查当前节点是否应该保留
      if (this.processSingleNodeForExtraction(child, extractType)) {
        if (child.isLeaf) {
          // 叶子节点直接添加
          if (child.isText) {
            // 文本节点需要特殊处理标记
            const processedFragment = this.processTextNodeForExtraction(child, extractType);
            if (processedFragment.size > 0) {
              processedFragment.forEach((n) => newChildren.push(n));
            }
          } else {
            newChildren.push(child.copy(child.content));
          }
        } else {
          // 非叶子节点递归处理其内容
          const processedContent = this.processNodeForExtraction(child, extractType);

          // 修复：即使内容为空也要保留节点结构，确保空节点不会丢失
          const newAttrs = { ...child.attrs };
          delete newAttrs[DIFF_CHANGE_ATTR_NAME];

          // 如果是 mermaid 节点，确保 mode 为 edit
          if (child.type.name === Mermaid.name) {
            newAttrs.mode = ViewMode.Edit;
          }

          newChildren.push(child.type.create(newAttrs, processedContent));
        }
      }
    });

    return Fragment.from(newChildren);
  }

  /**
   * 处理单个节点，决定是否保留
   * @param node 要处理的节点
   * @param extractType 提取类型
   * @returns 是否保留该节点
   */
  private processSingleNodeForExtraction(node: Node, extractType: DIFF_CHANGE_TYPE): boolean {
    // 检查节点是否有差异属性
    const diffAttr = node.attrs?.[DIFF_CHANGE_ATTR_NAME];

    if (!diffAttr) {
      // 没有差异属性的节点总是保留（原始内容）
      return true;
    }

    // 基于提取类型决定是否保留：
    // - 如果提取类型是 ADDED，保留 ADDED 和无标记的节点，过滤 REMOVED 节点
    // - 如果提取类型是 REMOVED，保留 REMOVED 和无标记的节点，过滤 ADDED 节点
    if (extractType === DIFF_CHANGE_TYPE.ADDED) {
      return diffAttr !== DIFF_CHANGE_TYPE.REMOVED;
    } else {
      return diffAttr !== DIFF_CHANGE_TYPE.ADDED;
    }
  }

  /**
   * 处理文本节点，根据标记决定是否保留
   * @param textNode 文本节点
   * @param extractType 提取类型
   * @returns 处理后的片段
   */
  private processTextNodeForExtraction(textNode: Node, extractType: DIFF_CHANGE_TYPE): Fragment {
    // 查找差异标记
    const diffMark = textNode.marks.find((mark) => mark.type.name === DIFF_CHANGE_NAME);

    if (!diffMark) {
      // 没有差异标记的文本节点保留
      return Fragment.from(textNode);
    }

    const diffMarkType = diffMark.attrs[DIFF_CHANGE_ATTR_NAME];

    // 基于提取类型和标记类型决定是否保留
    if (extractType === DIFF_CHANGE_TYPE.ADDED) {
      if (diffMarkType === DIFF_CHANGE_TYPE.REMOVED) {
        // 要提取添加的内容，但这是被删除的文本，所以不保留
        return Fragment.empty;
      } else {
        // 保留文本，但移除差异标记
        const newMarks = textNode.marks.filter((mark) => mark.type.name !== DIFF_CHANGE_NAME);
        return Fragment.from(textNode.type.schema.text(textNode.text!, newMarks));
      }
    } else {
      if (diffMarkType === DIFF_CHANGE_TYPE.ADDED) {
        // 要提取原始内容，但这是新添加的文本，所以不保留
        return Fragment.empty;
      } else {
        // 保留文本，但移除差异标记
        const newMarks = textNode.marks.filter((mark) => mark.type.name !== DIFF_CHANGE_NAME);
        return Fragment.from(textNode.type.schema.text(textNode.text!, newMarks));
      }
    }
  }

  containsDiffBlock(node: Node): boolean {
    // 检查当前节点是否是 diffblock
    if (node.type.name === DiffBlockExtensionName) {
      return true;
    }

    // 递归检查所有子节点
    let hasDiffBlock = false;
    node.forEach((child) => {
      if (this.containsDiffBlock(child)) {
        hasDiffBlock = true;
        return false; // 提前退出遍历
      }
    });

    return hasDiffBlock;
  }

  // async reportWriterData(params: ReportWriterDataParams) {
  //   await callHTTP('/api/v1/thought/reportDiffReviewEvent', {
  //     method: 'POST',
  //     body: params as unknown as Record<string, unknown>,
  //     silent: true,
  //   });
  // }
}

export const diffTransformUtils = new DiffTransformUtils();

export type IDiffTransformUtils = DiffTransformUtils;
