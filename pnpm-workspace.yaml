packages:
  - apps/*
  - packages/*
  - devops/workers/*

catalog:
  '@cloudflare/workers-types': ^4.20250719.0
  '@supabase/ssr': ^0.3.0
  '@supabase/supabase-js': ^2.46.1
  '@tiptap/core': 2.24.2
  '@tiptap/extension-blockquote': 2.24.2
  '@tiptap/extension-bold': 2.24.2
  '@tiptap/extension-bullet-list': 2.24.2
  '@tiptap/extension-code': 2.24.2
  '@tiptap/extension-code-block': 2.24.2
  '@tiptap/extension-collaboration': 2.24.2
  '@tiptap/extension-document': 2.24.2
  '@tiptap/extension-dropcursor': 2.24.2
  '@tiptap/extension-file-handler': 2.24.2
  '@tiptap/extension-gapcursor': 2.24.2
  '@tiptap/extension-hard-break': 2.24.2
  '@tiptap/extension-highlight': 2.24.2
  '@tiptap/extension-history': 2.24.2
  '@tiptap/extension-horizontal-rule': 2.24.2
  '@tiptap/extension-image': 2.24.2
  '@tiptap/extension-italic': 2.24.2
  '@tiptap/extension-link': 2.24.2
  '@tiptap/extension-list-item': 2.24.2
  '@tiptap/extension-mathematics': 2.24.2
  '@tiptap/extension-ordered-list': 2.24.2
  '@tiptap/extension-paragraph': 2.24.2
  '@tiptap/extension-placeholder': 2.24.2
  '@tiptap/extension-strike': 2.24.2
  '@tiptap/extension-table': 2.24.2
  '@tiptap/extension-table-cell': 2.24.2
  '@tiptap/extension-table-header': 2.24.2
  '@tiptap/extension-table-row': 2.24.2
  '@tiptap/extension-task-item': 2.24.2
  '@tiptap/extension-task-list': 2.24.2
  '@tiptap/extension-text': 2.24.2
  '@tiptap/extension-underline': 2.24.2
  '@tiptap/pm': 2.24.2
  '@tiptap/react': 2.24.2
  '@types/howler': ^2.2.12
  '@types/lodash': ^4.17.20
  '@types/lodash-es': ^4.17.12
  '@types/node': ^22.10.7
  '@types/react': ^18.3.18
  '@types/react-dom': ^18.3.5
  '@types/validator': ^13.12.2
  '@youmindinc/jsbridge': 0.1.3
  '@youmindinc/youcommon': ^0.1.22
  ahooks: ^3.8.1
  antd: ^5.24.3
  class-variance-authority: ^0.7.0
  clsx: ^2.1.1
  date-fns: ^3.6.0
  framer-motion: ^11.11.17
  franc-min: ^6.2.0
  highlight.js: ^11.10.0
  howler: ^2.2.4
  iso-639-3: ^3.0.1
  jotai: ^2.12.5
  jsdom: ^26.1.0
  katex: ^0.16.21
  lodash: ^4.17.21
  lodash-es: ^4.17.21
  lowlight: ^3.1.0
  lucide-react: 0.525.0
  markdown-it: ^14.1.0
  markdown-it-mark: ^4.0.0
  markdown-it-task-lists: ^2.1.1
  mermaid: ^11.4.1
  next: ^15.3.5
  prosemirror-markdown: ^1.13.2
  react: ^18.3.1
  react-dom: ^18.3.1
  react-hook-form: 7.60.0
  tailwind-merge: ^3.0.2
  tailwindcss: ^3.4.17
  tippy.js: ^6.3.7
  typescript: ^5.8.3
  usehooks-ts: ^3.1.0
  uuidv7: ^1.0.2
  validator: ^13.10.0
  wrangler: 4.25.0
  y-indexeddb: 9.0.12
  y-prosemirror: ^1.2.12
  yjs: ^13.6.20
  zod: ^3.23.8
  zod-validation-error: ^3.4.0
  "posthog-js": "^1.240.6"
  "react-router-dom": "^7.6.3"
